<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { DateTimePicker } from '@/components/ui/date-picker'
import { Plus, Trash2 } from 'lucide-vue-next'
import { CompetitionLevel } from '@/stores/matches'

const { t } = useI18n()

const props = defineProps<{
  currency?: string
  competitionLevel?: string
  maxPlayersAmount?: number
  maxPlayersAmountError?: string
  registrationEnds?: string
  publishAt?: string
  licenseRequired?: boolean
  international?: boolean
  withoutLimits?: boolean
  judges: string[]
}>()

const emit = defineEmits([
  'update:currency',
  'update:competitionLevel',
  'update:maxPlayersAmount',
  'update:registrationEnds',
  'update:publishAt',
  'update:licenseRequired',
  'update:international',
  'update:withoutLimits',
  'update:judges'
])

import { ref, watch } from 'vue'

const licenseRequiredModel = ref(props.licenseRequired ?? false)
const internationalModel = ref(props.international ?? false)
const withoutLimitsModel = ref(props.withoutLimits ?? false)

watch(() => props.licenseRequired, (val) => {
  licenseRequiredModel.value = val ?? false
})
watch(() => props.international, (val) => {
  internationalModel.value = val ?? false
})
watch(() => props.withoutLimits, (val) => {
  withoutLimitsModel.value = val ?? false
})

watch(licenseRequiredModel, (val) => {
  emit('update:licenseRequired', val)
})
watch(internationalModel, (val) => {
  emit('update:international', val)
})
watch(withoutLimitsModel, (val) => {
  emit('update:withoutLimits', val)
})

function addJudge() {
  emit('update:judges', [...props.judges, ''])
}

function removeJudge(index: number) {
  const newJudges = [...props.judges]
  newJudges.splice(index, 1)
  emit('update:judges', newJudges)
}

function updateJudge(index: number, value: string | number) {
  const newJudges = [...props.judges]
  newJudges[index] = String(value)
  emit('update:judges', newJudges)
}
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-end">
      <div class="space-y-2 flex-none">
        <Label for="currency">{{ t('matches.currency') }}</Label>
        <Select
          :model-value="currency"
          @update:model-value="emit('update:currency', $event)"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select currency" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PLN">PLN</SelectItem>
            <SelectItem value="EUR">EUR</SelectItem>
            <SelectItem value="USD">USD</SelectItem>
            <SelectItem value="CZK">CZK</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div class="space-y-2 flex-none">
        <Label for="maxPlayersAmount">{{ t('matches.maxPlayersAmount') }}</Label>
        <Input
          id="maxPlayersAmount"
          :model-value="maxPlayersAmount"
          @update:model-value="emit('update:maxPlayersAmount', $event ? Number($event) : undefined)"
          type="number"
          min="1"
          :placeholder="t('matches.maxPlayersAmount')"
          :class="maxPlayersAmountError ? 'border-destructive' : ''"
        />
        <p v-if="maxPlayersAmountError" class="text-sm text-destructive">{{ maxPlayersAmountError }}</p>
      </div>
      <div class="space-y-2 flex-none">
        <Label for="competitionLevel">{{ t('matches.competitionLevel') }}</Label>
        <Select
          :model-value="competitionLevel"
          @update:model-value="emit('update:competitionLevel', $event)"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select competition level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem :value="CompetitionLevel.FRIENDLY">Friendly</SelectItem>
            <SelectItem :value="CompetitionLevel.TOURNAMENT">Tournament</SelectItem>
            <SelectItem :value="CompetitionLevel.NATIONAL">National</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="space-y-2">
        <Label for="registrationEnds">{{ t('matches.registrationEnds') }}</Label>
        <DateTimePicker
          :model-value="registrationEnds"
          @update:model-value="emit('update:registrationEnds', $event)"
          placeholder="Select registration end date and time"
        />
      </div>
      <div class="space-y-2">
        <Label for="publishAt">{{ t('matches.publishAt') }}</Label>
        <DateTimePicker
          :model-value="publishAt"
          @update:model-value="emit('update:publishAt', $event)"
          placeholder="Select publish date and time"
        />
      </div>
    </div>

    <Separator />

    <div class="space-y-4">
      <div class="flex items-center space-x-2">
        <Checkbox
          id="licenseRequired"
          v-model="licenseRequiredModel"
        />
        <Label for="licenseRequired">{{ t('matches.licenseRequired') }}</Label>
      </div>

      <div class="flex items-center space-x-2">
        <Checkbox
          id="international"
          v-model="internationalModel"
        />
        <Label for="international">{{ t('matches.international') }}</Label>
      </div>

      <div class="flex items-center space-x-2">
        <Checkbox
          id="withoutLimits"
          v-model="withoutLimitsModel"
        />
        <Label for="withoutLimits">{{ t('matches.withoutLimits') }}</Label>
      </div>
    </div>

    <Separator />

    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <Label>{{ t('matches.judges') }}</Label>
        <Button @click="addJudge" variant="outline" size="sm">
          <Plus class="w-4 h-4 mr-2" />
          Add Judge
        </Button>
      </div>

      <div v-if="judges.length === 0" class="text-center py-4">
        <p class="text-muted-foreground">No judges added yet</p>
        <Button @click="addJudge" variant="outline" class="mt-2">
          <Plus class="w-4 h-4 mr-2" />
          Add Judge
        </Button>
      </div>

      <div v-else class="space-y-2">
        <div
          v-for="(judge, index) in judges"
          :key="index"
          class="flex items-center gap-2"
        >
          <Input
            :model-value="judge"
            @update:model-value="updateJudge(index, $event)"
            :placeholder="`Judge ${index + 1} name`"
            class="flex-1"
          />
          <Button
            @click="removeJudge(index)"
            variant="outline"
            size="sm"
            class="text-destructive hover:text-destructive"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
