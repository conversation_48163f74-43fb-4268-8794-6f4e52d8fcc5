<script setup lang="ts">
import { Badge } from '@/components/ui/badge'

defineProps<{
  currentStep: number
  totalSteps: number
  stepTitles: string[]
  stepErrors: number[]
}>()

const emit = defineEmits<{
  (e: 'go-to-step', step: number): void
}>()
</script>

<template>
  <div class="mb-8">
    <div class="flex items-center justify-between mb-2 flex-wrap gap-2">
      <button
        v-for="(title, index) in stepTitles"
        :key="index"
        class="text-sm font-medium flex items-center gap-2 px-2 py-1 rounded-md transition-colors hover:bg-muted/50 cursor-pointer relative"
        :class="currentStep === index + 1 ? 'bg-muted' : ''"
        @click="emit('go-to-step', index + 1)"
      >
        <div class="flex items-center gap-1">
          <Badge
            :variant="currentStep > index + 1 ? 'default' : currentStep === index + 1 ? 'secondary' : 'outline'"
            class="pointer-events-none"
          >
            {{ index + 1 }}
          </Badge>
        </div>
        <span :class="currentStep === index + 1 ? 'text-primary' : 'text-muted-foreground'" class="relative pr-3">
          {{ title }}
          <!-- Error indicator circle at top-right corner -->
          <span
            v-if="stepErrors[index] > 0"
            class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-md border-2 border-white"
          >
            {{ stepErrors[index] }}
          </span>
        </span>
      </button>
    </div>
    <div class="w-full bg-muted rounded-full h-2">
      <div
        class="bg-primary h-2 rounded-full transition-all duration-300"
        :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
      />
    </div>
  </div>
</template>
