<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'
import { Plus, Trash2, ChevronDown, ChevronUp } from 'lucide-vue-next'
import { MatchType, TypesOfGoals } from '@/stores/matches'
import type { AgendaEntry } from '@/stores/matches'
import dayjs from 'dayjs'

const { t } = useI18n()

const props = defineProps<{
  agendaItems: AgendaEntry[]
  startDate?: string
  endDate?: string
}>()

const emit = defineEmits<{
  (e: 'update:agendaItems', value: AgendaEntry[]): void
}>()

const expandedAgendaItems = ref<Set<number>>(new Set())

function toggleAgendaItem(index: number) {
  if (expandedAgendaItems.value.has(index)) {
    expandedAgendaItems.value.delete(index)
  } else {
    expandedAgendaItems.value.add(index)
  }
}

function isAgendaItemExpanded(index: number) {
  return expandedAgendaItems.value.has(index)
}

function addAgendaItem() {
  const newIndex = props.agendaItems.length
  const newItems = [
    ...props.agendaItems,
    {
      name: '',
      description: '',
      location: '',
      date: '',
      time: '',
      goals: '',
      format: '',
      punctation: [],
      typesOfGoals: '',
      numberOfArrows: '',
      competitionBranch: ''
    }
  ]
  emit('update:agendaItems', newItems)
  expandedAgendaItems.value.add(newIndex)
}

function removeAgendaItem(index: number) {
  const newItems = [...props.agendaItems]
  newItems.splice(index, 1)
  emit('update:agendaItems', newItems)

  const newExpanded = new Set<number>()
  expandedAgendaItems.value.forEach(expandedIndex => {
    if (expandedIndex < index) {
      newExpanded.add(expandedIndex)
    } else if (expandedIndex > index) {
      newExpanded.add(expandedIndex - 1)
    }
  })
  expandedAgendaItems.value = newExpanded
}

const agendaDateErrors = computed(() => {
  const errors: Record<number, string> = {}

  if (!props.startDate) return errors

  const start = dayjs(props.startDate)
  const end = props.endDate ? dayjs(props.endDate) : start

  props.agendaItems.forEach((item, index) => {
    if (item.date) {
      const agendaDate = dayjs(item.date)

      if (agendaDate.isBefore(start, 'day')) {
        errors[index] = t('validation.agendaDateBeforeStart')
      } else if (agendaDate.isAfter(end, 'day')) {
        errors[index] = t('validation.agendaDateAfterEnd')
      }
    }
  })

  return errors
})
</script>

<template>
  <div class="space-y-3">
    <div class="flex items-center justify-between">
      <Label class="text-sm font-semibold">{{ t('matches.agendaInfo') }}</Label>
      <Button @click="addAgendaItem" variant="outline" size="sm">
        <Plus class="w-4 h-4 mr-2" />
        {{ t('matches.addAgendaItem') }}
      </Button>
    </div>

    <div v-if="agendaItems.length === 0" class="text-center py-4">
      <p class="text-muted-foreground text-sm">No agenda items yet</p>
      <Button @click="addAgendaItem" variant="outline" size="sm" class="mt-2">
        <Plus class="w-4 h-4 mr-2" />
        {{ t('matches.addAgendaItem') }}
      </Button>
    </div>

    <div v-else class="space-y-2">
      <div
        v-for="(item, index) in agendaItems"
        :key="index"
        class="border rounded-lg overflow-hidden"
      >
        <!-- Collapsed Header -->
        <div
          class="p-2 bg-muted/30 cursor-pointer flex items-center justify-between hover:bg-muted/50 transition-colors"
          @click="toggleAgendaItem(index)"
        >
          <div class="flex items-center gap-2 flex-1 min-w-0">
            <div class="flex items-center gap-1">
              <component
                :is="isAgendaItemExpanded(index) ? ChevronUp : ChevronDown"
                class="w-3 h-3 text-muted-foreground"
              />
              <span class="font-medium text-sm">{{ item.name || `Agenda Item ${index + 1}` }}</span>
            </div>
            <div class="flex items-center gap-2 text-xs text-muted-foreground truncate">
              <span v-if="item.date">{{ item.date }}</span>
              <span v-if="item.time">{{ item.time }}</span>
              <span v-if="item.location" class="truncate">{{ item.location }}</span>
            </div>
          </div>
          <Button
            @click.stop="removeAgendaItem(index)"
            variant="outline"
            size="sm"
            class="text-destructive hover:text-destructive h-6 w-6 p-0"
          >
            <Trash2 class="w-3 h-3" />
          </Button>
        </div>

        <!-- Expanded Content -->
        <div v-if="isAgendaItemExpanded(index)" class="p-4 space-y-3 border-t bg-background">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div class="space-y-1">
              <Label :for="`agenda-name-${index}`">Name *</Label>
              <Input
                :id="`agenda-name-${index}`"
                v-model="item.name"
                placeholder="Event name"
              />
            </div>
            <div class="space-y-1">
              <Label :for="`agenda-location-${index}`">Location</Label>
              <Input
                :id="`agenda-location-${index}`"
                v-model="item.location"
                placeholder="Event location"
              />
            </div>
          </div>

          <div class="space-y-1">
            <Label :for="`agenda-description-${index}`">Description</Label>
            <Textarea
              :id="`agenda-description-${index}`"
              v-model="item.description"
              placeholder="Event description"
              rows="2"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div class="space-y-1">
              <Label :for="`agenda-date-${index}`">Date</Label>
              <DatePicker
                v-model="item.date"
                placeholder="Select date"
                :class="agendaDateErrors[index] ? 'border-destructive' : ''"
              />
              <p v-if="agendaDateErrors[index]" class="text-sm text-destructive">
                {{ agendaDateErrors[index] }}
              </p>
            </div>
            <div class="space-y-1">
              <Label :for="`agenda-time-${index}`">Time</Label>
              <Input
                :id="`agenda-time-${index}`"
                v-model="item.time"
                type="time"
                placeholder="Event time"
              />
            </div>
            <div class="space-y-1">
              <Label :for="`agenda-branch-${index}`">Competition Branch</Label>
              <Select
                :model-value="item.competitionBranch"
                @update:model-value="item.competitionBranch = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="MatchType.THREED">3D</SelectItem>
                  <SelectItem :value="MatchType.FIELD">Field</SelectItem>
                  <SelectItem :value="MatchType.OUTDOOR">Outdoor</SelectItem>
                  <SelectItem :value="MatchType.INDOOR">Indoor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div class="space-y-1">
              <Label :for="`agenda-goals-${index}`">Goals</Label>
              <Input
                :id="`agenda-goals-${index}`"
                v-model="item.goals"
                placeholder="Number of goals"
              />
            </div>
            <div class="space-y-1">
              <Label :for="`agenda-arrows-${index}`">Number of Arrows</Label>
              <Input
                :id="`agenda-arrows-${index}`"
                v-model="item.numberOfArrows"
                placeholder="Number of arrows"
              />
            </div>
            <div class="space-y-1">
              <Label :for="`agenda-types-${index}`">Types of Goals</Label>
              <Select
                :model-value="item.typesOfGoals"
                @update:model-value="item.typesOfGoals = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="TypesOfGoals.THREED">3D</SelectItem>
                  <SelectItem :value="TypesOfGoals.PAPER">Paper</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
