# Implementation Plan

- [x] 1. Configure dayjs plugins and fix date comparison errors
  - Set up dayjs with required plugins for date comparison methods
  - Update components to use properly configured dayjs instance
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 1.1 Create dayjs configuration file
  - Write `src/lib/dayjs.ts` with isSameOrAfter and isSameOrBefore plugins
  - Export configured dayjs instance for application use
  - _Requirements: 4.1, 4.2_

- [x] 1.2 Update MatchesContainer to use configured dayjs
  - Replace direct dayjs imports with configured dayjs instance in `src/components/matches/MatchesContainer.vue`
  - Fix all date comparison method calls to use properly configured dayjs
  - _Requirements: 4.3, 4.4_

- [x] 2. Fix Vue component emit definitions
  - Update all components with incomplete emit definitions
  - Ensure all emitted events are properly typed
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2.1 Fix Step1BasicInfo component emits
  - Update `src/components/matches/create/Step1BasicInfo.vue` emit definitions to include all events
  - Add missing emit events: update:name, update:email, update:coverImageUrl
  - _Requirements: 1.1, 1.2, 1.4_

- [x] 2.2 Fix Step2Location component emits
  - Update `src/components/matches/create/Step2Location.vue` emit definitions to include all location events
  - Add missing emit events: update:country, update:city, update:address, handle postcode type conversion
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2.3 Fix Step6Settings component emits
  - Update `src/components/matches/create/Step6Settings.vue` emit definitions to include all settings events
  - Add missing emit events: update:currency, update:competitionLevel, fix judges array handling
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 3. Implement null safety fixes
  - Add proper null checks and optional chaining throughout the application
  - Handle potentially null activePlayer and user references
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 3.1 Fix GearSwitcher null safety
  - Update `src/components/GearSwitcher.vue` to handle null activePlayer.value safely
  - Add null checks before accessing equipment and activeEquipmentId properties
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 3.2 Fix NavUser null safety
  - Update `src/components/NavUser.vue` to handle optional user name property
  - Add null checks before accessing user.name and provide fallback values
  - _Requirements: 2.1, 2.2, 2.4_

- [x] 3.3 Fix user store null safety
  - Update `src/stores/user.ts` to handle optional equipment property on player objects
  - Add null checks and type guards for player equipment access
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 4. Fix missing required properties in Match objects
  - Add isActive property to all Match object creations
  - Update test data and component usage to include required properties
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 4.1 Fix CreateMatchForm Match object creation
  - Update `src/components/matches/CreateMatchForm.vue` to include isActive property in Match creation
  - Handle optional properties properly in form data binding
  - _Requirements: 3.1, 3.2_

- [x] 4.2 Fix test files with incomplete Match objects
  - Update all test files to include isActive property in Match object creation
  - Fix compilation.spec.ts and component-compatibility.spec.ts test data
  - _Requirements: 3.3, 3.4_

- [x] 5. Fix component prop type mismatches
  - Correct type mismatches in component prop passing
  - Handle prop spreading and type conversion issues
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 5.1 Fix WeatherIcon size prop type
  - Update `src/components/matches/WeatherIcon.vue` to handle string|number size prop correctly
  - Convert string size values to numbers or update prop type definition
  - _Requirements: 5.1, 5.3_

- [x] 5.2 Fix Drawer component prop spreading
  - Update `src/components/ui/drawer/Drawer.vue` to handle prop spreading safely
  - Add proper type checking for forwarded props
  - _Requirements: 5.1, 5.4_

- [x] 6. Fix store and service type issues
  - Update store interfaces to include all referenced properties and methods
  - Fix service method signatures and property access
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 6.1 Fix equipment store service references
  - Update equipment store to properly expose equipmentService property
  - Fix test mocking to match actual store interface
  - _Requirements: 6.1, 6.2_

- [x] 6.2 Fix user store equipment property access
  - Update user store to handle optional equipment property on player objects
  - Add proper type definitions for player equipment relationships
  - _Requirements: 6.1, 6.4_

- [x] 7. Fix payment and division data type inconsistencies
  - Handle different payment data formats consistently
  - Fix equipment category property access issues
  - _Requirements: 8.1, 8.2, 8.3, 8.4_

- [x] 7.1 Fix payment data type handling
  - Update `src/components/matches/MatchDetailsWidget.vue` to handle both Record<string, number> and PaymentEntry[] formats
  - Add type guards to safely access payment data
  - _Requirements: 8.1, 8.4_

- [x] 7.2 Fix equipment category property access
  - Update `src/components/matches/MatchSignupWidget.vue` to handle optional short_name property
  - Add fallback values for missing equipment category properties
  - _Requirements: 8.3_

- [x] 8. Fix remaining test compilation errors
  - Update all test files to compile without TypeScript errors
  - Fix mock objects and test assertions
  - _Requirements: 7.1, 7.2, 7.3, 7.4_

- [x] 8.1 Fix equipment store test mocking
  - Update `src/stores/__tests__/equipment.spec.ts` to properly mock equipmentService
  - Fix createEquipment method calls to include all required properties
  - _Requirements: 7.1, 7.2_

- [x] 8.2 Fix component compatibility test assertions
  - Update test assertions to handle potentially undefined values safely
  - Add proper null checks in test expectations
  - _Requirements: 7.3, 7.4_

- [x] 9. Verify compilation and run final type check
  - Run TypeScript compilation to ensure all errors are resolved
  - Verify no new errors were introduced during fixes
  - _Requirements: All requirements verification_

- [x] 9.1 Run type-check script and verify zero errors
  - Execute `pnpm type-check` to confirm all TypeScript errors are resolved
  - Document any remaining issues and create follow-up tasks if needed
  - _Requirements: All requirements verification_
