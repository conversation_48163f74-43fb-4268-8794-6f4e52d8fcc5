import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import type { Federation, FederationData, FederationPatch } from '../api/feathers-client'
export type { Federation } from '../api/feathers-client'
import { api } from '../api/feathers-client'

// Types for the JSON fields in federations
export interface AgeDivision {
  id?: number | string // optional id, used in custom definitions in matches and tournaments
  name: string
  short_name: string
  min?: number | string
  max?: number | string
  arrows?: string[],
  isOpen?: boolean
}

export interface StyleDivision {
  id?: number | string // optional id, used in custom definitions in matches and tournaments
  name: string
  short_name: string
  arrows?: string[]
  bow_types?: string[]
  equipments?: string[]
  accessories?: string[]
  bow_wood_types?: string[]
  shot_techniques?: string[]
}

// Extended federation type with parsed JSON fields
export interface FederationWithParsedData extends Omit<Federation, 'ageDivisions' | 'styleDivisions'> {
  ageDivisions?: AgeDivision[]
  styleDivisions?: StyleDivision[]
  disciplines?: string[]
}

/**
 * Federations store for managing federation data with caching.
 * Since federations are few and changes are rare, we cache all federations
 * on first use and provide methods to refresh when needed.
 *
 * Usage:
 * ```ts
 * const federationsStore = useFederationsStore()
 *
 * // Get all federations (cached after first call)
 * const federations = await federationsStore.getAllFederations()
 *
 * // Get specific federation by ID
 * const federation = federationsStore.getFederationById(1)
 *
 * // Refresh cache
 * await federationsStore.refreshFederations()
 * ```
 */

export const useFederationsStore = defineStore('federations', () => {
  // Use the typed service from the api client
  const federationsService = api.federations

  const federations = ref<FederationWithParsedData[]>([])
  const isLoading = ref(false)
  const error = ref<Error | null>(null)
  const isInitialized = ref(false)

  // Helper function to parse JSON fields
  function parseFederationData(federation: Federation): FederationWithParsedData {
    const {
      ageDivisions,
      styleDivisions,
      disciplines,
      ...rest
    } = federation

    const parsed: FederationWithParsedData = {
      ...rest,
      // Parse ageDivisions if it's a string
      ageDivisions: typeof ageDivisions === 'string'
        ? (() => {
          try {
            return JSON.parse(ageDivisions) as AgeDivision[]
          } catch (err) {
            console.warn('Failed to parse ageDivisions for federation', federation.id, err)
            return undefined
          }
        })()
        : (ageDivisions as AgeDivision[] | undefined),

      // Parse styleDivisions if it's a string
      styleDivisions: typeof styleDivisions === 'string'
        ? (() => {
          try {
            return JSON.parse(styleDivisions) as StyleDivision[]
          } catch (err) {
            console.warn('Failed to parse styleDivisions for federation', federation.id, err)
            return undefined
          }
        })()
        : (styleDivisions as StyleDivision[] | undefined),

      // Parse disciplines if it's a string
      disciplines: typeof disciplines === 'string'
        ? (() => {
          try {
            return JSON.parse(disciplines)
          } catch (err) {
            console.warn('Failed to parse disciplines for federation', federation.id, err)
            return undefined
          }
        })()
        : disciplines
    }

    return parsed
  }

  // Computed getters
  const activeFederations = computed(() =>
    federations.value.filter(federation => federation.isActive)
  )

  const getFederationById = computed(() => (id: number) =>
    federations.value.find(federation => federation.id === id)
  )

  const getFederationByName = computed(() => (name: string) =>
    federations.value.find(federation => federation.name === name)
  )

  // Actions
  async function fetchFederations() {
    isLoading.value = true
    error.value = null

    try {
      const result = await federationsService.find({
        query: {
          $sort: { id: 1 }
        }
      })

      const federationsList = Array.isArray(result.data) ? result.data : result as unknown as Federation[]

      // Parse JSON fields for each federation
      federations.value = federationsList.map(parseFederationData)
      isInitialized.value = true

      return federations.value
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch federations')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function getAllFederations() {
    if (!isInitialized.value) {
      await fetchFederations()
    }
    return federations.value
  }

  async function refreshFederations() {
    isInitialized.value = false
    return await fetchFederations()
  }

  async function getFederation(id: number) {
    try {
      const result = await federationsService.get(id)
      return parseFederationData(result)
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to fetch federation with id ${id}`)
      }
    }
  }

  async function createFederation(data: FederationData) {
    try {
      const newFederation = await federationsService.create(data)
      const parsed = parseFederationData(newFederation)

      // Add to cache
      federations.value.push(parsed)

      return parsed
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create federation')
      }
    }
  }

  async function updateFederation(id: number, data: FederationPatch) {
    try {
      const updatedFederation = await federationsService.patch(id, data)
      const parsed = parseFederationData(updatedFederation)

      // Update in cache
      const index = federations.value.findIndex(f => f.id === id)
      if (index !== -1) {
        federations.value[index] = parsed
      }

      return parsed
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update federation with id ${id}`)
      }
    }
  }

  async function deleteFederation(id: number) {
    try {
      const removedFederation = await federationsService.remove(id)

      // Remove from cache
      const index = federations.value.findIndex(f => f.id === id)
      if (index !== -1) {
        federations.value.splice(index, 1)
      }

      return removedFederation
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to delete federation with id ${id}`)
      }
    }
  }

  function clearCache() {
    federations.value = []
    isInitialized.value = false
    error.value = null
  }

  return {
    // State
    federations,
    isLoading,
    error,
    isInitialized,

    // Computed
    activeFederations,
    getFederationById,
    getFederationByName,

    // Actions
    getAllFederations,
    refreshFederations,
    getFederation,
    createFederation,
    updateFederation,
    deleteFederation,
    clearCache,
  }
})
