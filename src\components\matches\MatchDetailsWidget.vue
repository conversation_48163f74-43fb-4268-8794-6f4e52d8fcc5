<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { usePlayerDivisions } from '@/composables/usePlayerDivisions'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import type { Match as ApiMatch } from '@/api/feathers-client'
import { fetchSunriseSunset, formatDateForApi, SunriseSunsetErrorMessages } from '@/services/sunriseSunset'
import { getWeatherForecast } from '@/services/weatherService'
import WeatherIcon from './WeatherIcon.vue'
import MatchSignupWidget from './MatchSignupWidget.vue'
import PlayerResultWidget from './PlayerResultWidget.vue'
import type { ExtendedMatch, AgeCategory, PaymentEntry, DisplayEquipmentCategory, DisplayAgeCategory, DisplayStyleCategory, DisplayDataType } from '@/types'

// ... other imports ...
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  Users,
  Navigation,
  Sunrise,
  Sunset,
  Thermometer,
  CloudSun,
  Sun,
  ShieldAlert,
  Award,
  Info as InfoIcon,
  Crosshair,
  Skull,
  X as XIcon,
  Eye as EyeIcon,
  Phone,
  Mail,
  Trophy,
  MapPin,
  Home,
  Building
} from 'lucide-vue-next'

// Using centralized types from @/types

interface WeatherDataFromApi {
  hourly?: { // Kept for potential future use, though daily is primary now
    time: string[];
    temperature_2m: number[];
    relative_humidity_2m: number[];
    wind_speed_10m: number[];
  };
  daily?: {
    time: string[];
    weather_code?: number[]; // Added weather_code
    temperature_2m_max?: number[];
    temperature_2m_min?: number[];
    sunrise?: string[];
    sunset?: string[];
    // precipitation_sum?: number[]; // Removed as per recent service changes
  };
  // Include other top-level fields from WeatherData in weatherService if needed
  daily_units?: Record<string, string>;
  timezone?: string;
  // ... etc.
}

// Using centralized types from @/types

const props = defineProps<{
  match?: ApiMatch | null
}>()

const { t, locale } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

console.log('activePlayer:', activePlayer.value)

const fetchedSunriseTime = ref<string | null>(null)
const fetchedSunsetTime = ref<string | null>(null)
const isFetchingSunTimes = ref(false)

// Add refs for weather data
const fetchedWeatherData = ref<WeatherDataFromApi | null>(null)
const isFetchingWeather = ref(false)
const weatherError = ref<string | null>(null)

// Cache for mocked data per match ID
const mockedDataCache = ref(new Map<number, { rivals: number; difficulty: number }>());

function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

const currentMatch = computed<ExtendedMatch | null>(() => {
  if (props.match) {
    return props.match as ExtendedMatch;
  }
  return null;
})

const getMockedDataForMatch = (matchId: number) => {
  if (!mockedDataCache.value.has(matchId)) {
    mockedDataCache.value.set(matchId, {
      rivals: Math.floor(Math.random() * 30) + 1,
      difficulty: Math.floor(Math.random() * 91) + 10, // 10-100%
    });
  }
  return mockedDataCache.value.get(matchId)!; // Safe due to the check above
};

const mockedRivalsCount = computed(() => {
  if (currentMatch.value && currentMatch.value.id != null) {
    return getMockedDataForMatch(currentMatch.value.id).rivals;
  }
  return 0; // Default if no match or ID
});

const mockedDifficultyPercentage = computed(() => {
  if (currentMatch.value && currentMatch.value.id != null) {
    return getMockedDataForMatch(currentMatch.value.id).difficulty;
  }
  return 0; // Default if no match or ID
});

const progressBarColorStyle = computed(() => {
  const percentage = mockedDifficultyPercentage.value;
  // Handle no match case for progress bar, or 0% explicitly
  if ((!currentMatch.value && percentage === 0) || percentage === 0) return 'bg-muted';
  if (percentage < 33) return 'bg-green-500';
  if (percentage < 66) return 'bg-yellow-500';
  return 'bg-red-500';
});

// const weatherIconComponent = computed(() => CloudSun); // Remove old computed

// New computed for weather code
const currentWeatherCode = computed<number | null>(() => {
  if (isFetchingWeather.value || weatherError.value || !fetchedWeatherData.value?.daily?.weather_code?.[0]) {
    return null; // Return null if loading, error, or no code
  }
  return fetchedWeatherData.value.daily.weather_code[0];
});

// Function that uses sunriseSunset service to fetch data
async function getSunriseSunsetData(latitude: number, longitude: number, date?: string) {
  if (!latitude || !longitude) {
    fetchedSunriseTime.value = SunriseSunsetErrorMessages.LOCATION_MISSING;
    fetchedSunsetTime.value = SunriseSunsetErrorMessages.LOCATION_MISSING;
    return;
  }

  isFetchingSunTimes.value = true;
  fetchedSunriseTime.value = SunriseSunsetErrorMessages.LOADING;
  fetchedSunsetTime.value = SunriseSunsetErrorMessages.LOADING;

  try {
    const result = await fetchSunriseSunset(latitude, longitude, date);

    if ('message' in result) {
      // It's an error
      fetchedSunriseTime.value = SunriseSunsetErrorMessages.API_ERROR;
      fetchedSunsetTime.value = SunriseSunsetErrorMessages.API_ERROR;
    } else {
      // It's a successful result
      fetchedSunriseTime.value = result.sunrise;
      fetchedSunsetTime.value = result.sunset;
    }
  } finally {
    isFetchingSunTimes.value = false;
  }
}

// Watch for changes in currentMatch to fetch sunrise/sunset times
watch(currentMatch, (newMatch, oldMatch) => {
  // Reset fetched times on match change
  if (newMatch?.id !== oldMatch?.id) {
    fetchedSunriseTime.value = null;
    fetchedSunsetTime.value = null;
  }

  if (newMatch && newMatch.latitude != null && newMatch.longitude != null) {
    const apiDate = formatDateForApi(newMatch.startDate);
    getSunriseSunsetData(newMatch.latitude, newMatch.longitude, apiDate || undefined);
  } else {
    // Reset if no match or no lat/lng
    fetchedSunriseTime.value = newMatch ? SunriseSunsetErrorMessages.LOCATION_MISSING : null;
    fetchedSunsetTime.value = newMatch ? SunriseSunsetErrorMessages.LOCATION_MISSING : null;
  }
}, { immediate: true }); // immediate: true to run on initial load if currentMatch is already set

// Watch for changes in currentMatch to fetch weather data
watch(currentMatch, async (newMatch, oldMatch) => {
  if (newMatch?.id !== oldMatch?.id) {
    fetchedWeatherData.value = null;
    weatherError.value = null;
  }

  if (newMatch && newMatch.latitude != null && newMatch.longitude != null && newMatch.startDate) {
    isFetchingWeather.value = true;
    weatherError.value = null;
    try {
      const weatherDate = new Date(newMatch.startDate);
      fetchedWeatherData.value = await getWeatherForecast({
        latitude: newMatch.latitude,
        longitude: newMatch.longitude,
        date: weatherDate,
      });
    } catch (error) {
      console.error('Error fetching weather:', error);
      weatherError.value = 'Failed to load weather data.';
      fetchedWeatherData.value = null;
    } finally {
      isFetchingWeather.value = false;
    }
  } else {
    fetchedWeatherData.value = null;
    if (newMatch) {
        weatherError.value = 'Location or date missing for weather forecast.';
    }
  }
}, { immediate: true });

const displayData = computed<DisplayDataType | null>(() => {
  const match = currentMatch.value;
  if (!match) return null;

  // Calculate distance using MatchItem logic
  let calculatedDistanceKm: string = 'N/A';
  // Use activePlayer lat/lng from user store for distance calculation
  const playerLat = typeof (activePlayer.value as any)?.latitude === 'number' ? (activePlayer.value as any).latitude : undefined;
  const playerLng = typeof (activePlayer.value as any)?.longitude === 'number' ? (activePlayer.value as any).longitude : undefined;
  const matchLat = match.latitude;
  const matchLng = match.longitude;

  if (
    typeof playerLat === 'number' &&
    typeof playerLng === 'number' &&
    typeof matchLat === 'number' &&
    typeof matchLng === 'number'
  ) {
    const R = 6371; // Radius of the Earth in km
    const dLat = deg2rad(matchLat - playerLat);
    const dLon = deg2rad(matchLng - playerLng);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(playerLat)) *
      Math.cos(deg2rad(matchLat)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distanceKm = R * c;
    calculatedDistanceKm = distanceKm.toFixed(0); // Only the number as string
  }



  // Process equipment categories (keeping old logic for backward compatibility)
  const equipmentCategories: DisplayEquipmentCategory[] = (Array.isArray(match.equipmentCategories)
    ? match.equipmentCategories.map((item: any, index: number) => {
        // Handle both string and object cases
        if (typeof item === 'string') {
          // For string items, look up in federation for proper name (if available)
          const federationCategory = lookupFederationStyleCategory(item);
          return {
            id: index,
            name: federationCategory?.name || item,
            short_name: item,
          };
        } else if (typeof item === 'object' && item !== null) {
          // Handle object case
          const nameStr = item.name || 'Equipment';
          return {
            id: item.id || index,
            name: nameStr,
            short_name: item.short_name || item.code || nameStr || 'N/A',
          };
        } else {
          return {
            id: index,
            name: 'Equipment',
            short_name: 'N/A',
          };
        }
      })
    : []);

  // Helper function to look up federation division by short_name
  const lookupFederationAgeCategory = (shortName: string) => {
    if (match.federation && typeof match.federation === 'object' && match.federation.ageDivisions) {
      const federationAgeCategories = Array.isArray(match.federation.ageDivisions)
        ? match.federation.ageDivisions
        : [];
      return federationAgeCategories.find((cat: any) => cat.short_name === shortName);
    }
    return null;
  };

  const lookupFederationStyleCategory = (shortName: string) => {
    if (match.federation && typeof match.federation === 'object' && match.federation.styleDivisions) {
      const federationStyleCategories = Array.isArray(match.federation.styleDivisions)
        ? match.federation.styleDivisions
        : [];
      return federationStyleCategories.find((cat: any) => cat.short_name === shortName);
    }
    return null;
  };

  // Process age categories - check both ageDivisions (new) and ageCategories (old)
  const ageCategoriesSource = match.ageDivisions || match.ageCategories || [];
  const ageCategories: DisplayAgeCategory[] = (Array.isArray(ageCategoriesSource)
    ? ageCategoriesSource.map((item: any, index: number) => {
        // Handle both string and object cases
        if (typeof item === 'string') {
          // For string items, look up in federation for proper name
          const federationCategory = lookupFederationAgeCategory(item);
          return {
            id: index,
            name: federationCategory?.name || item,
            short_name: item,
            min: federationCategory?.min ?? federationCategory?.min_age ?? null,
            max: federationCategory?.max ?? federationCategory?.max_age ?? null,
          };
        } else if (typeof item === 'object' && item !== null) {
          // Handle object case
          const nameStr = item.name || 'Category';
          return {
            id: item.id || index,
            name: nameStr,
            short_name: item.short_name || item.abbreviation || nameStr.substring(0, 3).toUpperCase() || 'N/A',
            min: item.min_age ?? item.min ?? null,
            max: item.max_age ?? item.max ?? null,
          };
        } else {
          return {
            id: index,
            name: 'Category',
            short_name: 'N/A',
            min: null,
            max: null,
          };
        }
      })
    : []);

  // Process style categories - check both styleDivisions (new) and equipmentCategories (old)
  const styleCategoriesSource = match.styleDivisions || match.equipmentCategories || [];
  const styleCategories: DisplayStyleCategory[] = (Array.isArray(styleCategoriesSource)
    ? styleCategoriesSource.map((item: any, index: number) => {
        // Handle both string and object cases
        if (typeof item === 'string') {
          // For string items, look up in federation for proper name
          const federationCategory = lookupFederationStyleCategory(item);
          return {
            id: index,
            name: federationCategory?.name || item,
            short_name: item,
          };
        } else if (typeof item === 'object' && item !== null) {
          // Handle object case
          const nameStr = item.name || 'Style';
          return {
            id: item.id || index,
            name: nameStr,
            short_name: item.short_name || item.code || nameStr.substring(0, 3).toUpperCase() || 'N/A',
          };
        } else {
          return {
            id: index,
            name: 'Style',
            short_name: 'N/A',
          };
        }
      })
    : []);

  // Calculate player's age if birthdate is available
  const calculatePlayerAge = (birthdate: string | undefined): number | null => {
    if (!birthdate) return null;
    const today = new Date();
    const birth = new Date(birthdate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  // Find the correct age category for the active player
  const findPlayerAgeCategory = (playerAge: number | null, ageCategories: DisplayAgeCategory[]): DisplayAgeCategory | null => {
    if (playerAge === null || !ageCategories.length) return null;

    return ageCategories.find(cat => {
      const minAge = typeof cat.min === 'string' ? parseInt(cat.min, 10) : cat.min;
      const maxAge = typeof cat.max === 'string' ? parseInt(cat.max, 10) : cat.max;

      if (minAge != null && maxAge != null) {
        return playerAge >= minAge && playerAge <= maxAge;
      }
      if (minAge != null) {
        return playerAge >= minAge;
      }
      if (maxAge != null) {
        return playerAge <= maxAge;
      }
      return false;
    }) || null;
  };

  // Handle price - try to find price for player's age category first
  let priceAmount = 0;
  const finalPriceCurrency = match.currency || 'PLN';

  // Type guard to check if payments is Record<string, number>
  const isPaymentRecord = (payments: any): payments is Record<string, number> => {
    return payments && typeof payments === 'object' && !Array.isArray(payments) &&
           Object.values(payments).every(value => typeof value === 'number');
  };

  // Type guard to check if payments is PaymentEntry[]
  const isPaymentEntryArray = (payments: any): payments is PaymentEntry[] => {
    return Array.isArray(payments) && payments.every(entry =>
      typeof entry === 'object' && entry !== null &&
      typeof entry.amount === 'number'
    );
  };

  if (match.payments) {
    const playerAge = calculatePlayerAge((activePlayer.value as any)?.birthdate);
    const playerAgeCategory = findPlayerAgeCategory(playerAge, ageCategories);

    if (isPaymentRecord(match.payments)) {
      // Handle Record<string, number> format
      if (playerAgeCategory && match.payments[playerAgeCategory.short_name]) {
        priceAmount = match.payments[playerAgeCategory.short_name];
      } else {
        // Fallback to first available price
        const firstKey = Object.keys(match.payments)[0];
        if (firstKey) {
          priceAmount = match.payments[firstKey];
        }
      }
    } else if (isPaymentEntryArray(match.payments)) {
      // Handle PaymentEntry[] format
      let selectedEntry: PaymentEntry | undefined;

      if (playerAgeCategory) {
        // Try to find payment entry for player's age category
        selectedEntry = match.payments.find(entry =>
          entry.category === playerAgeCategory.short_name
        );
      }

      if (!selectedEntry && match.payments.length > 0) {
        // Fallback to first available payment entry
        selectedEntry = match.payments[0];
      }

      if (selectedEntry) {
        priceAmount = selectedEntry.amount;
        // Use currency from payment entry if available, otherwise use match currency
        if (selectedEntry.currency) {
          // Update currency from payment entry
          const finalPriceCurrencyFromEntry = selectedEntry.currency;
        }
      }
    }
  }

  const organizerName = match.organizer?.name ?? 'N/A';

  let temperatureDisplay: number | string = 'N/A';
  if (isFetchingWeather.value) {
    temperatureDisplay = 'Loading...';
  } else if (weatherError.value) {
    temperatureDisplay = 'Error';
  } else if (fetchedWeatherData.value) {
    if (fetchedWeatherData.value.daily?.temperature_2m_max?.[0] !== undefined) {
      temperatureDisplay = fetchedWeatherData.value.daily.temperature_2m_max[0];
    } else if (fetchedWeatherData.value.hourly?.temperature_2m?.[0] !== undefined) {
      // Attempt to find a temperature around noon for hourly, or just take the first one
      const noonIndex = fetchedWeatherData.value.hourly.time.findIndex(t => t.endsWith('T12:00'));
      temperatureDisplay = fetchedWeatherData.value.hourly.temperature_2m[noonIndex !== -1 ? noonIndex : 0];
    } else {
      temperatureDisplay = 'N/A';
    }
  }

  const result: DisplayDataType = {
    id: match.id,
    name: match.name,
    country: match.country,
    city: match.city,
    startDate: match.startDate,
    endDate: match.endDate,
    equipmentCategories: equipmentCategories,
    ageCategories: ageCategories,
    styleCategories: styleCategories,
    federation: typeof match.federation === 'object' && match.federation?.name ? match.federation.name : match.federation as string | undefined,
    licenseRequired: match.licenseRequired,
    organizerName,
    competitionRank: match.competitionLevel ?? match.competition_level ?? 'Local',
    competitionType: (match.international ?? match.is_international) ? 'International' : (match.matchType ?? match.match_type ?? 'N/A'),
    distanceKm: calculatedDistanceKm, // Now just the number as string, e.g. '12' or 'N/A'
    sunriseTime: fetchedSunriseTime.value || (isFetchingSunTimes.value ? 'Loading...' : (match.sunriseTime ?? '06:00')),
    sunsetTime: fetchedSunsetTime.value || (isFetchingSunTimes.value ? 'Loading...' : (match.sunsetTime ?? '18:00')),
    temperatureCelsius: typeof temperatureDisplay === 'number' ? temperatureDisplay : (match.temperatureCelsius ?? 20) as number,
    priceAmount: priceAmount,
    priceCurrency: finalPriceCurrency,
    participantsCount: mockedRivalsCount.value,
    participantProgress: mockedDifficultyPercentage.value,
  };
  return result;
});

// Get player divisions - this will be reactive to activePlayer and currentMatch changes
const playerDivisions = usePlayerDivisions(activePlayer, currentMatch)

const formatDate = (dateString?: string | Date) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString);
  return date.toLocaleDateString('pl-PL', { year: 'numeric', month: '2-digit', day: '2-digit' });
};

const matchDurationDays = computed(() => {
  if (!displayData.value?.startDate || !displayData.value?.endDate) return 'N/A';
  const start = new Date(displayData.value.startDate);
  const end = new Date(displayData.value.endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.max(1, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  return `${diffDays} ${t('common.day', { count: diffDays })}`;
});

const getFlagUrl = (countryCode?: string) => {
  if (!countryCode) return '';
  return `/flags/${countryCode.toLowerCase()}.svg`;
};

// Note: This function is no longer needed since we're using local state
// Parent components should handle match selection/deselection
const handleCloseDetails = () => {
  // This could emit an event to parent component if needed
  console.log('Close details requested - parent should handle this')
}

// Navigation functions
const navigateToMatchDetails = () => {
  if (currentMatch.value) {
    router.push({ name: 'match-details', params: { id: currentMatch.value.id.toString() } })
  }
}

const navigateToOrganizerDetails = () => {
  if (currentMatch.value?.organizer?.id) {
    router.push({ name: 'organizer-details', params: { id: currentMatch.value.organizer.id.toString() } })
  } else if (currentMatch.value?.organizerId) {
    router.push({ name: 'organizer-details', params: { id: currentMatch.value.organizerId.toString() } })
  }
}

const navigateToTournamentDetails = () => {
  if (currentMatch.value?.tournament) {
    const tournamentId = typeof currentMatch.value.tournament === 'object'
      ? currentMatch.value.tournament.id
      : currentMatch.value.tournamentId
    if (tournamentId) {
      router.push({ name: 'tournament-details', params: { id: tournamentId.toString() } })
    }
  }
}

// Computed property for short weekday name
const matchStartShortWeekday = computed(() => {
  if (!displayData.value?.startDate) return '';
  const date = new Date(displayData.value.startDate);
  return date.toLocaleDateString(locale.value, { weekday: 'short' });
});

</script>

<template>
  <TooltipProvider>
    <div v-if="displayData" class="p-4 space-y-4 text-sm">
      <!-- Player Result Widget (compact version) -->
      <PlayerResultWidget :match="currentMatch" :compact="true" />

      <!-- Header: Sign Up & Icons -->
      <div class="flex items-center justify-between">
        <MatchSignupWidget :match="currentMatch" :show-label="true" />
        <div class="flex items-center space-x-1.5">
          <Button variant="ghost" size="icon" class="w-7 h-7" @click="navigateToMatchDetails">
            <EyeIcon class="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="icon" class="w-7 h-7" @click="handleCloseDetails">
            <XIcon class="w-5 h-5" />
          </Button>
        </div>
      </div>

      <!-- Match Title & Basic Info -->
      <div>
        <h2 class="text-xl font-semibold leading-tight mb-1">
          {{ displayData.name }}
        </h2>

        <!-- Tournament Info (if available) -->
        <div v-if="currentMatch?.tournament" class="flex items-center gap-2 mb-2">
          <Trophy class="w-4 h-4 text-muted-foreground" />
          <button
            @click="navigateToTournamentDetails"
            class="text-primary hover:underline text-sm font-medium"
          >
            {{ typeof currentMatch.tournament === 'object' ? currentMatch.tournament.name : currentMatch.tournament }}
          </button>
        </div>

        <div class="flex items-center space-x-2 text-muted-foreground">
          <img v-if="displayData.country" :src="getFlagUrl(displayData.country)" :alt="displayData.country" class="w-4 h-auto" />
          <span>{{ displayData.city }}</span>
          <span>{{ formatDate(displayData.startDate) }}</span>
          <span>| {{ matchStartShortWeekday }} | {{ matchDurationDays }}</span>

        </div>
      </div>

      <!-- Icons Row & Price -->
      <div class="flex items-center justify-between text-muted-foreground">
        <div class="flex items-center space-x-3">
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Navigation class="w-4 h-4" />
              <span>{{ displayData.distanceKm }} km</span>
            </TooltipTrigger>
            <TooltipContent>{{ t('matches.distanceToEvent') }}</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Sunrise class="w-4 h-4" />
              <span>{{ displayData.sunriseTime }}</span>
            </TooltipTrigger>
            <TooltipContent>{{ t('matches.sunrise') }}</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Sunset class="w-4 h-4" />
              <span>{{ displayData.sunsetTime }}</span>
            </TooltipTrigger>
            <TooltipContent>{{ t('matches.sunset') }}</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Thermometer class="w-4 h-4" />
              <span>
                {{ typeof displayData.temperatureCelsius === 'number' ? displayData.temperatureCelsius.toFixed(0) + '°C' : displayData.temperatureCelsius }}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              {{ t('matches.temperature') }}
              <span v-if="weatherError"> ({{ weatherError }})</span>
              <span v-else-if="isFetchingWeather"> ({{ t('common.loading') }})</span>
              <span v-else-if="fetchedWeatherData?.daily?.temperature_2m_min?.[0] !== undefined && fetchedWeatherData?.daily?.temperature_2m_max?.[0] !== undefined">
                ({{ t('matches.min') }}: {{ fetchedWeatherData.daily.temperature_2m_min[0].toFixed(0) }}°C {{ t('matches.max') }}: {{ fetchedWeatherData.daily.temperature_2m_max[0].toFixed(0) }}°C)
              </span>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <!-- Use WeatherIcon component -->
              <WeatherIcon v-if="currentWeatherCode !== null" :weather-code="currentWeatherCode" :size="16" />
              <span v-else-if="isFetchingWeather">...</span>
              <CloudSun v-else class="w-4 h-4" /> <!-- Fallback icon -->
            </TooltipTrigger>
            <TooltipContent>
              {{ t('matches.weather') }}
              <span v-if="weatherError"> ({{ weatherError }})</span>
              <span v-else-if="isFetchingWeather"> ({{ t('common.loading') }})</span>
              <span v-else-if="currentWeatherCode === null"> ({{ t('common.na') }})</span>
            </TooltipContent>
          </Tooltip>
        </div>
        <Badge variant="outline" class="text-base px-3 py-1">
          {{ displayData.priceAmount }} {{ displayData.priceCurrency }}
        </Badge>
      </div>

      <!-- View Details Button -->
      <div class="flex justify-center">
        <Button
          variant="outline"
          class="w-full"
          @click="navigateToMatchDetails"
        >
          <EyeIcon class="w-4 h-4 mr-2" />
          {{ t('matches.viewFullDetails') }}
        </Button>
      </div>

      <Separator />

      <!-- Competitors/Rivals -->
      <div>
        <h3 class="font-semibold mb-2">{{ t('matches.competitorsRivals') }}</h3>
        <div class="flex flex-wrap gap-2 mb-2">
          <Tooltip v-if="playerDivisions.styleDivision.value">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ playerDivisions.styleDivision.value.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ playerDivisions.styleDivision.value.name }}</TooltipContent>
          </Tooltip>
          <Tooltip v-if="playerDivisions.ageDivision.value">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ playerDivisions.ageDivision.value.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ playerDivisions.ageDivision.value.name }}</TooltipContent>
          </Tooltip>
          <Tooltip v-if="playerDivisions.sexDivision.value">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ playerDivisions.sexDivision.value.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ playerDivisions.sexDivision.value.name }}</TooltipContent>
          </Tooltip>
        </div>
        <div class="flex items-center space-x-3 text-muted-foreground">
          <Users class="w-4 h-4 flex-shrink-0" />
          <span class="mr-2 flex-shrink-0">{{ displayData.participantsCount }}</span>

          <Crosshair class="w-4 h-4 flex-shrink-0" />
          <span class="mr-2 flex-shrink-0">{{ displayData.participantProgress?.toFixed(0) }}%</span>

          <Skull class="w-5 h-5 flex-shrink-0" :class="[progressBarColorStyle.includes('red') ? 'text-red-500' : progressBarColorStyle.includes('yellow') ? 'text-yellow-500' : 'text-green-500']" />
          <div class="flex-1 bg-muted rounded-full h-2.5 min-w-[50px]">
            <div class="h-2.5 rounded-full" :class="progressBarColorStyle" :style="{ width: displayData.participantProgress + '%' }"></div>
          </div>
        </div>
      </div>

      <Separator />

      <!-- Equipment Categories -->
      <div v-if="displayData.equipmentCategories && displayData.equipmentCategories.length > 0">
        <h3 class="font-semibold mb-2">{{ t('matches.equipmentCategories') }}</h3>
        <div class="flex flex-wrap gap-2">
          <Tooltip v-for="cat in displayData.equipmentCategories" :key="cat.id">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ cat.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ cat.name }}</TooltipContent>
          </Tooltip>
        </div>
      </div>

      <!-- Age Categories -->
      <div v-if="displayData.ageCategories && displayData.ageCategories.length > 0">
        <h3 class="font-semibold mb-2 mt-3">{{ t('matches.ageCategories') }}</h3>
        <div class="flex flex-wrap gap-2">
          <Tooltip v-for="cat in displayData.ageCategories" :key="cat.id">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ cat.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>
              <span v-if="cat.min && cat.max">{{ t('matches.ageRange', { min: cat.min, max: cat.max }) }}</span>
              <span v-else-if="cat.min">{{ t('matches.ageMin', { min: cat.min }) }}</span>
              <span v-else-if="cat.max">{{ t('matches.ageMax', { max: cat.max }) }}</span>
              <span v-else>{{ cat.name }}</span> <!-- Fallback to name if no min/max -->
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <!-- Style Categories -->
      <div v-if="displayData.styleCategories && displayData.styleCategories.length > 0">
        <h3 class="font-semibold mb-2 mt-3">{{ t('matches.styleCategories') }}</h3>
        <div class="flex flex-wrap gap-2">
          <Tooltip v-for="cat in displayData.styleCategories" :key="cat.id">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ cat.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ cat.name }}</TooltipContent>
          </Tooltip>
        </div>
      </div>

      <Separator />

      <!-- Info Section -->
      <div>
        <h3 class="font-semibold mb-3 flex items-center">
          <InfoIcon class="w-5 h-5 mr-2 text-muted-foreground" />
          {{ t('matches.info') }}
        </h3>
        <div class="grid grid-cols-[max-content_1fr] gap-x-4 gap-y-1.5 text-muted-foreground">
          <span class="font-medium text-foreground">{{ t('matches.organizer') }}:</span>
          <button
            @click="navigateToOrganizerDetails"
            class="text-primary hover:underline cursor-pointer text-left"
            :disabled="!currentMatch?.organizer?.id && !currentMatch?.organizerId"
          >
            {{ displayData.organizerName }}
          </button>

          <span class="font-medium text-foreground">{{ t('matches.competitionRank') }}:</span>
          <span>{{ displayData.competitionRank }}</span>

          <span class="font-medium text-foreground">{{ t('matches.competitionType') }}:</span>
          <span>{{ displayData.competitionType }}</span>

          <span class="font-medium text-foreground">{{ t('matches.federation') }}:</span>
          <span>{{ displayData.federation || t('common.na') }}</span>

          <span class="font-medium text-foreground">{{ t('matches.license') }}:</span>
          <span>{{ displayData.licenseRequired ? t('common.required') : t('common.notRequired') }}</span>

          <!-- Contact Information -->
          <template v-if="currentMatch?.phone || currentMatch?.email">
            <span class="font-medium text-foreground">{{ t('matches.phone') }}:</span>
            <div v-if="currentMatch?.phone" class="flex items-center gap-1">
              <Phone class="w-3 h-3" />
              <a :href="`tel:${currentMatch.phone}`" class="text-primary hover:underline">
                {{ currentMatch.phone }}
              </a>
            </div>
            <span v-else>{{ t('common.na') }}</span>

            <span class="font-medium text-foreground">{{ t('matches.email') }}:</span>
            <div v-if="currentMatch?.email" class="flex items-center gap-1">
              <Mail class="w-3 h-3" />
              <a :href="`mailto:${currentMatch.email}`" class="text-primary hover:underline">
                {{ currentMatch.email }}
              </a>
            </div>
            <span v-else>{{ t('common.na') }}</span>
          </template>
        </div>
      </div>

      <Separator />

      <!-- Address Section -->
      <div v-if="currentMatch?.address || currentMatch?.city || currentMatch?.country || currentMatch?.postcode">
        <h3 class="font-semibold mb-3 flex items-center">
          <MapPin class="w-5 h-5 mr-2 text-muted-foreground" />
          {{ t('matches.address') }}
        </h3>
        <div class="space-y-1.5 text-muted-foreground">
          <div v-if="currentMatch?.address" class="flex items-start gap-2">
            <Home class="w-4 h-4 mt-0.5 flex-shrink-0" />
            <span>{{ currentMatch.address }}</span>
          </div>
          <div v-if="currentMatch?.city || currentMatch?.postcode" class="flex items-center gap-2">
            <Building class="w-4 h-4 flex-shrink-0" />
            <span>
              <template v-if="currentMatch?.postcode">{{ currentMatch.postcode }}</template>
              <template v-if="currentMatch?.postcode && currentMatch?.city"> </template>
              <template v-if="currentMatch?.city">{{ currentMatch.city }}</template>
            </span>
          </div>
          <div v-if="currentMatch?.country" class="flex items-center gap-2">
            <img v-if="currentMatch.country" :src="getFlagUrl(currentMatch.country)" :alt="currentMatch.country" class="w-4 h-auto" />
            <span>{{ currentMatch.country }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Fallback when no match data is provided -->
    <div v-else class="p-4 text-center text-muted-foreground">
      {{ t('matches.noMatchSelected') }}
    </div>

  </TooltipProvider>
</template>
