import { promises as fsPromises, writeFileSync, appendFileSync } from 'fs'
import path from 'path'

import { parse as csvParse } from 'csv-parse/sync'

import { app } from '../src/app'
import type { EquipmentData } from '../src/services/equipment/equipment.schema'

function parseBool(val: string): boolean {
    return val === '1' || val === 'true'
}

function parseString(value: string | undefined): string | undefined {
    if (!value || value === '\N' || value === 'NULL' || value === 'null' || value === '') {
        return undefined;
    }
    return value.trim();
}

async function importEquipment(csvFile: string) {
    const absPath = path.resolve(csvFile)
    let fileContent = await fsPromises.readFile(absPath, 'utf8')
    // Remove UTF-8 BOM if present
    if (fileContent.charCodeAt(0) === 0xFEFF) {
        fileContent = fileContent.slice(1)
    }
    const records: any[] = csvParse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        delimiter: ';'
    })

    const players = app.service('players')
    const equipment = app.service('equipment')
    const federationsService = app.service('federations')

    // Cache federations
    const federationsList = await federationsService.find({ paginate: false });
    const federationsMap = new Map(federationsList.map((f: any) => [f.legacyId, f.id]));

    const errorLogPath = path.join(process.cwd(), 'import-equipment-errors.txt');
    writeFileSync(errorLogPath, ''); // Clear log file
    let errorCount = 0;
    let successCount = 0;

    for (const row of records) {
        try {
            // Parse arrays for configuration merge
            const parseArrayField = (value: string | undefined): any[] => {
                if (!value) return [];
                // Aggressively remove \N and trim whitespace
                const cleanedValue = value.replace(/\\N/g, '').trim();
                if (cleanedValue === '' || cleanedValue === '-1' || cleanedValue === '[]') return [];

                try {
                    const parsed = JSON.parse(cleanedValue);
                    return Array.isArray(parsed) ? parsed : [];
                } catch (error) {
                    console.error(`Error parsing array value: ${value}`, error);
                    return [];
                }
            };

            // Find player by legacy ID
            let playerId: number | undefined = undefined;
            if (row['player_id'] && row['player_id'] !== '\N') {
                const legacyPlayerId = Number(row['player_id']);
                try {
                    const playerResult = await players.find({
                        query: { legacyId: legacyPlayerId, $limit: 1 }
                    });
                    if (playerResult.data && playerResult.data.length > 0) {
                        playerId = playerResult.data[0].id;
                    } else {
                        throw new Error(`Player with legacyId ${legacyPlayerId} not found`);
                    }
                } catch (err) {
                    throw new Error(`Error finding player with legacyId ${legacyPlayerId}: ${(err as Error).message}`);
                }
            }

            // Map equipmentClass
            let equipmentClass = 'BOW'; // default
            if (row['equipment'] === 'kusza') {
                equipmentClass = 'CROSSBOW';
            }

            // Merge configuration fields
            const valueMapping: { [key: string]: string } = {
                'plastikowe lotki': 'AR_PLF',
                'naturalne lotki': 'AR_NLF',
                'strzały aluminiowe i karbonowe': 'AR_ALC',
                'strzały drewniane': 'AR_WD',
                'łuk refleksyjny sportowy': 'BT_RSP',
                'łuk drewniany': 'BT_WD',
                'łuk bloczkowy': 'BT_CB',
                'łuk': 'EQ_BOW',
                'stabilizator do 12"': 'AC_STU12',
                'stabilizator pow 12"': 'AC_STO12',
                'celowniki - regulowany': 'AC_SADJ',
                'lipko': 'AC_SCOP',
                'poziomica': 'AC_LVL',
                '1-3 pin celownik': 'AC_S13P',
                '4-5 pin celownik': 'AC_S45P',
                'podstawka pod strzałę': 'AC_AREST',
                'peep sight': 'AC_PEEP',
                'kisser': 'AC_KISS',
                'metalowe siodełka na strzałę': 'AC_NPM',
                'tradycyjne siodełka na strzałę': 'AC_NPT',
                'rękawiczka/skórka': 'AC_GLOV',
                'tłumik cięciw': 'AC_SIL',
                'ciężarki': 'AC_WGT',
                'button': 'AC_BTN',
                'clicker': 'AC_CLK',
                'łuk refleksyjny': 'BW_REC',
                'łuk prosty': 'BW_STR',
                'łuk historyczny': 'BW_HIS',
                'strzelanie "palec wskazujący pod strzałą"': 'ST_IFU',
                'strzelanie "śródziemnomorski"': 'ST_MED',
                'strzelanie "string walking"': 'ST_SW',
                'spust zwalniający': 'ST_REL',
                'zekier': 'ST_THR'
            };

            const accessories = parseArrayField(row['accessories']).map((item) => valueMapping[item] || item);
            const arrows = parseArrayField(row['arrows']).map((item) => valueMapping[item] || item);
            const shotTechniques = parseArrayField(row['shot_techniques']).map((item) => valueMapping[item] || item);

            const configuration = {
                accessories,
                arrows,
                techniques: shotTechniques // rename shot_techniques to techniques
            };

            const federationStylesRaw = parseArrayField(row['equipment_categories']);
            const federationStyles = [];
            for (const style of federationStylesRaw) {
                const federationId = federationsMap.get(Number(style.federation_id));

                if (federationId) {
                    federationStyles.push({
                        styleDivision: style.category,
                        federationId: federationId,
                        federationName: style.federation_name
                    });
                } else {
                    console.warn(`Federation with legacyId ${style.federation_id} not found, skipping style.`);
                }
            }
            const bowType = parseString(row['bow_type']);
            const data: EquipmentData = {
                legacyId: Number(row['id']),
                name: parseString(row['name']) || 'Unnamed Equipment',
                equipmentClass,
                type: bowType ? valueMapping[bowType] || bowType : undefined,
                configuration: JSON.stringify(configuration),
                federationStyles: JSON.stringify( federationStyles) as any,
                isActive: parseBool(row['active']) ?? true,
                playerId,
                isDefault: parseBool(row['default']) ?? false
            };

            const cleanData = Object.fromEntries(
                Object.entries(data).filter(([_, v]) => v !== undefined)
            ) as EquipmentData;

            await equipment.create(cleanData);
            successCount++;
            console.log(`Imported equipment: ${cleanData.name} (legacyId: ${row['id']}, playerId: ${playerId})`);
        } catch (err) {
            errorCount++;
            const errorMessage = `Failed to import equipment with id=${row['id']}, name=${row['name']}: ${(err as Error).message}`;
            console.error(errorMessage);
            appendFileSync(errorLogPath, `${errorMessage}\nRecord: ${JSON.stringify(row)}\n\n`);
        }
    }

    console.log('\nEquipment import complete.');
    console.log(`- Successful imports: ${successCount}`);
    console.log(`- Failed imports: ${errorCount}`);
    if (errorCount > 0) {
        console.log(`- See ${errorLogPath} for details.`);
    }
}

if (process.argv.length < 3) {
    console.error('Usage: pnpm tsx scripts/import-equipment.ts <path-to-csv>')
    process.exit(1)
}

importEquipment(process.argv[2])
    .then(() => {
        process.exit(0)
    })
    .catch((err) => {
        console.error('Import failed:', err)
        process.exit(1)
    })