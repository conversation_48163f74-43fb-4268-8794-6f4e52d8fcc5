<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'
import { MatchType } from '@/stores/matches'
import type { Federation } from '@/stores/federations'
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import '@toast-ui/editor/dist/toastui-editor.css'
import { Editor } from '@toast-ui/editor'

const { t } = useI18n()

const props = defineProps<{
  name: string
  nameError?: string
  matchType?: string
  federationId?: number
  federations: Federation[]
  coverImageUrl?: string
  coverImageUrlError?: string
  startDate: string
  startDateError?: string
  endDate?: string
  endDateError?: string
  description?: string
  email?: string
  emailError?: string
  phone?: string
}>()

const emit = defineEmits<{
  (e: 'update:name', value: string | number): void
  (e: 'update:matchType', value: string | undefined): void
  (e: 'update:federationId', value: number | undefined): void
  (e: 'update:coverImageUrl', value: string | number): void
  (e: 'update:startDate', value: string): void
  (e: 'update:endDate', value: string | undefined): void
  (e: 'update:description', value: string): void
  (e: 'update:email', value: string | number): void
  (e: 'update:phone', value: string | number): void
}>()

const editorRef = ref<HTMLElement | null>(null)
const editorInstance = ref<InstanceType<typeof Editor> | null>(null)

function initializeEditor() {
  if (editorRef.value && !editorInstance.value) {
    editorInstance.value = new Editor({
      el: editorRef.value,
      height: '300px',
      initialEditType: 'wysiwyg',
      previewStyle: 'vertical',
      hideModeSwitch: true,
      placeholder: t('matches.description'),
      initialValue: props.description || '',
      events: {
        change: () => {
          if (editorInstance.value) {
            emit('update:description', editorInstance.value.getHTML())
          }
        }
      }
    })
  }
}

function destroyEditor() {
  if (editorInstance.value) {
    editorInstance.value.destroy()
    editorInstance.value = null
  }
}

function updateEditorContent(content: string) {
  if (editorInstance.value && editorInstance.value.getHTML() !== content) {
    editorInstance.value.setHTML(content || '')
  }
}

onMounted(() => {
  nextTick(() => {
    initializeEditor()
  })
})

onUnmounted(() => {
  destroyEditor()
})

watch(() => props.description, (newDescription) => {
  updateEditorContent(newDescription || '')
})
</script>

<template>
  <div class="space-y-4">
    <!-- First row: Name (half), Match Type (quarter), Federation (quarter) -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="space-y-2 md:col-span-2">
        <Label for="name">{{ t('matches.name') }} *</Label>
        <Input
          id="name"
          :model-value="name"
          @update:model-value="emit('update:name', $event)"
          :placeholder="t('matches.name')"
          :class="nameError ? 'border-destructive' : ''"
          required
        />
        <p v-if="nameError" class="text-sm text-destructive">{{ nameError }}</p>
      </div>
      <div class="space-y-2 md:col-span-1">
        <Label for="matchType">{{ t('matches.matchType') }}</Label>
        <Select
          :model-value="matchType"
          @update:model-value="emit('update:matchType', $event as string | undefined)"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select match type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem :value="MatchType.THREED">3D</SelectItem>
            <SelectItem :value="MatchType.FIELD">Field</SelectItem>
            <SelectItem :value="MatchType.OUTDOOR">Outdoor</SelectItem>
            <SelectItem :value="MatchType.INDOOR">Indoor</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div class="space-y-2 md:col-span-1">
        <Label for="federation">{{ t('matches.federation') }}</Label>
        <Select
          :model-value="federationId?.toString()"
          @update:model-value="emit('update:federationId', $event ? parseInt($event as string) : undefined)"
        >
          <SelectTrigger>
            <SelectValue :placeholder="t('matches.selectFederation')" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="federation in federations"
              :key="federation.id"
              :value="federation.id.toString()"
            >
              {{ federation.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- Cover image: separate row -->
    <div class="space-y-2">
      <Label for="coverImageUrl">{{ t('matches.coverImageUrl') }}</Label>
      <Input
        id="coverImageUrl"
        :model-value="coverImageUrl"
        @update:model-value="emit('update:coverImageUrl', $event)"
        :placeholder="t('matches.coverImageUrl')"
        type="url"
        :class="coverImageUrlError ? 'border-destructive' : ''"
      />
      <p v-if="coverImageUrlError" class="text-sm text-destructive">{{ coverImageUrlError }}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="space-y-2">
        <Label for="startDate">{{ t('matches.startDate') }} *</Label>
        <DatePicker
          :model-value="startDate"
          @update:model-value="emit('update:startDate', $event as string)"
          placeholder="Select start date"
          :class="startDateError ? 'border-destructive' : ''"
        />
        <p v-if="startDateError" class="text-sm text-destructive">{{ startDateError }}</p>
      </div>
      <div class="space-y-2">
        <Label for="endDate">{{ t('matches.endDate') }}</Label>
        <DatePicker
          :model-value="endDate"
          @update:model-value="emit('update:endDate', $event as string | undefined)"
          placeholder="Select end date"
          :class="endDateError ? 'border-destructive' : ''"
        />
        <p v-if="endDateError" class="text-sm text-destructive">{{ endDateError }}</p>
      </div>
    </div>

    <!-- Email and phone row above description -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="space-y-2">
        <Label for="email">{{ t('matches.email') }}</Label>
        <Input
          id="email"
          :model-value="email"
          @update:model-value="emit('update:email', $event)"
          :placeholder="t('matches.email')"
          type="email"
          :class="emailError ? 'border-destructive' : ''"
        />
        <p v-if="emailError" class="text-sm text-destructive">{{ emailError }}</p>
      </div>
      <div class="space-y-2">
        <Label for="phone">{{ t('matches.phone') }}</Label>
        <Input
          id="phone"
          :model-value="phone"
          @update:model-value="emit('update:phone', $event)"
          :placeholder="t('matches.phone')"
          type="tel"
        />
      </div>
    </div>

    <div class="space-y-2">
      <Label for="description">{{ t('matches.description') }}</Label>
      <div ref="editorRef" class="border rounded-md"></div>
    </div>
  </div>
</template>
