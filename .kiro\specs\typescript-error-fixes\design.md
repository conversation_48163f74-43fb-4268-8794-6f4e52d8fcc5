# Design Document

## Overview

This design outlines the systematic approach to fixing the 97 TypeScript compilation errors in the Archery Points application. The errors fall into several categories: Vue component emit definitions, null safety issues, missing required properties, dayjs plugin configuration, component prop type mismatches, store/service type issues, test compilation errors, and inconsistent data type handling. The solution involves targeted fixes for each category while maintaining application functionality and type safety.

## Architecture

### Error Categories Analysis

Based on the compilation output, the errors can be categorized as follows:

1. **Vue Component Emit Errors (15+ errors)**: Missing emit definitions in Step1BasicInfo, Step2Location, Step6Settings components
2. **Null Safety Errors (10+ errors)**: activePlayer.value potentially null, optional property access
3. **Missing Required Properties (20+ errors)**: isActive property missing from Match objects in tests
4. **Dayjs Plugin Errors (12+ errors)**: isSameOrAfter and isSameOrBefore methods not available
5. **Component Prop Type Errors (8+ errors)**: Type mismatches in WeatherIcon, Drawer components
6. **Store/Service Type Errors (15+ errors)**: Missing properties in user store, equipment service references
7. **Test Compilation Errors (20+ errors)**: Mock objects missing required properties
8. **Data Type Inconsistencies (7+ errors)**: Payment data format conflicts, equipment category property access

### Solution Architecture

The fix strategy follows a layered approach:

```
Configuration Layer
├── dayjs plugin configuration
├── TypeScript strict null checks handling
└── Vue component type definitions

Component Layer
├── Emit definition fixes
├── Prop type corrections
└── Null safety implementations

Store/Service Layer
├── Interface completeness
├── Property existence validation
└── Service method signatures

Test Layer
├── Mock object completeness
├── Type assertion safety
└── Test data validation
```

## Components and Interfaces

### 1. Vue Component Emit Fixes

**Files to Fix:**

- `src/components/matches/create/Step1BasicInfo.vue`
- `src/components/matches/create/Step2Location.vue`
- `src/components/matches/create/Step6Settings.vue`

**Strategy:**

```typescript
// Current problematic emit definition
const emit = defineEmits<{
  (e: 'update:phone', value: string): void
}>()

// Fixed comprehensive emit definition
const emit = defineEmits<{
  (e: 'update:name', value: string): void
  (e: 'update:email', value: string): void
  (e: 'update:phone', value: string): void
  (e: 'update:coverImageUrl', value: string): void
  (e: 'update:country', value: string): void
  (e: 'update:postcode', value: string | number): void
  (e: 'update:city', value: string): void
  (e: 'update:address', value: string): void
  (e: 'update:currency', value: string): void
  (e: 'update:competitionLevel', value: string): void
  (e: 'update:judges', value: string[]): void
}>()
```

### 2. Null Safety Implementation

**Files to Fix:**

- `src/components/GearSwitcher.vue`
- `src/components/NavUser.vue`
- `src/stores/user.ts`

**Strategy:**

```typescript
// Current problematic code
return activePlayer.value.equipment.find((e: Equipment) => e.id === activePlayer.value.activeEquipmentId)

// Fixed with null safety
return activePlayer.value?.equipment?.find((e: Equipment) => e.id === activePlayer.value?.activeEquipmentId)

// Alternative with explicit null check
if (!activePlayer.value) return null
return activePlayer.value.equipment.find((e: Equipment) => e.id === activePlayer.value.activeEquipmentId)
```

### 3. Dayjs Plugin Configuration

**File to Create/Update:**

- `src/lib/dayjs.ts` (new configuration file)

**Strategy:**

```typescript
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export default dayjs
```

**Files to Update:**

- `src/components/matches/MatchesContainer.vue`

Replace direct dayjs imports with the configured version.

### 4. Required Property Fixes

**Files to Fix:**

- All test files with Match object creation
- `src/components/matches/CreateMatchForm.vue`

**Strategy:**

```typescript
// Current problematic test data
const baseMatch: Match = {
  id: 1,
  name: 'Test Match',
  organizerId: 1
}

// Fixed with required properties
const baseMatch: Match = {
  id: 1,
  name: 'Test Match',
  organizerId: 1,
  isActive: true // Add required property
}
```

## Data Models

### Enhanced Type Definitions

```typescript
// Enhanced Match interface with proper optional handling
interface ExtendedMatch extends Match {
  // Ensure base required properties are present
  isActive: boolean
  
  // Optional computed fields
  distanceKm?: number
  sunriseTime?: string
  sunsetTime?: string
  temperatureCelsius?: number
  participantProgress?: number
  participantsCount?: number
  
  // Handle payment data type variations
  payments?: Record<string, number> | PaymentEntry[]
  
  // Properly typed nested objects
  judges?: string[]
  ageDivisions?: AgeDivision[]
  styleDivisions?: StyleDivision[]
}

// Equipment category with consistent property access
interface EquipmentCategory {
  id: string | number
  name: string
  short_name?: string // Make optional to handle missing property
  description?: string
}

// User interface with optional name property
interface User {
  id: number
  email: string
  name?: string // Handle optional name property
  avatar?: string
  isActive?: boolean
}
```

### Store Interface Enhancements

```typescript
// Equipment store interface
interface EquipmentStore {
  isLoading: Ref<boolean>
  error: Ref<Error | null>
  equipmentService: EquipmentService // Ensure service is properly typed
  createEquipment: (data: EquipmentCreateData) => Promise<Equipment>
  updateEquipment: (id: number, data: Partial<Equipment>) => Promise<Equipment>
  deleteEquipment: (id: number) => Promise<void>
}

// Equipment creation data with all required fields
interface EquipmentCreateData {
  name: string
  category?: string
  playerId?: number
  isActive: boolean
  isDefault: boolean
  type?: string
  legacyId?: number
  equipmentClass?: string
  configuration?: unknown
  federationStyles?: unknown
}
```

## Error Handling

### Null Safety Strategy

1. **Optional Chaining**: Use `?.` operator for potentially null/undefined values
2. **Null Checks**: Explicit null checks before property access
3. **Default Values**: Provide fallback values for optional properties
4. **Type Guards**: Implement type guard functions for complex type checking

### Type Assertion Strategy

1. **Gradual Typing**: Fix errors incrementally without breaking existing functionality
2. **Type Narrowing**: Use type guards to narrow types safely
3. **Generic Constraints**: Use proper generic constraints for flexible typing
4. **Union Type Handling**: Properly handle union types with type discrimination

## Testing Strategy

### Test Data Completeness

1. **Mock Object Validation**: Ensure all mock objects have required properties
2. **Type-Safe Assertions**: Use type-safe assertion methods
3. **Optional Property Handling**: Properly handle optional properties in tests
4. **Service Mocking**: Create properly typed service mocks

### Component Testing

1. **Prop Validation**: Ensure all required props are provided in tests
2. **Emit Testing**: Test component emits with correct type signatures
3. **Store Integration**: Test store integration with proper type safety
4. **Error Boundary Testing**: Test error handling with type-safe approaches

## Implementation Plan

### Phase 1: Configuration and Infrastructure

1. **Dayjs Configuration**: Set up dayjs with required plugins
2. **Type Definition Updates**: Update core type definitions
3. **Store Interface Fixes**: Fix store and service interfaces

### Phase 2: Component Fixes

1. **Emit Definition Fixes**: Fix all Vue component emit definitions
2. **Null Safety Implementation**: Add null safety checks
3. **Prop Type Corrections**: Fix component prop type mismatches

### Phase 3: Test Fixes

1. **Mock Object Completion**: Add missing required properties to test mocks
2. **Type Assertion Safety**: Fix unsafe type assertions in tests
3. **Test Data Validation**: Ensure all test data is type-compliant

### Phase 4: Data Handling Consistency

1. **Payment Data Normalization**: Handle payment data format variations
2. **Equipment Category Fixes**: Fix equipment category property access
3. **Form Data Type Safety**: Ensure form data maintains type safety

## Benefits

1. **Zero Compilation Errors**: Achieve clean TypeScript compilation
2. **Enhanced Type Safety**: Improve runtime safety through better typing
3. **Better Developer Experience**: Eliminate TypeScript errors that slow development
4. **Maintainable Codebase**: Create a more maintainable and predictable codebase
5. **Test Reliability**: Ensure tests compile and run correctly
6. **Future-Proof Architecture**: Establish patterns for handling similar issues in the future
