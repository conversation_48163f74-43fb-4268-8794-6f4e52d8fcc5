import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { createI18n } from 'vue-i18n'

// Import types to test
import type { ExtendedMatch, TournamentWithMatches } from '@/types'

// Mock components for testing
const MockSimpleMatchesList = {
    name: 'MockSimpleMatchesList',
    props: {
        items: {
            type: Array as () => ExtendedMatch[],
            required: true
        }
    },
    template: '<div data-testid="matches-list">{{ items.length }} matches</div>'
}

const MockTournamentItem = {
    name: 'MockTournamentItem',
    props: {
        tournament: {
            type: Object as () => TournamentWithMatches,
            required: true
        },
        isSelected: {
            type: Boolean,
            default: false
        }
    },
    emits: ['select', 'hover'],
    template: '<div data-testid="tournament-item">{{ tournament.name }}</div>'
}

// Setup test utilities
const createTestRouter = () => {
    return createRouter({
        history: createWebHistory(),
        routes: [
            { path: '/', component: { template: '<div>Home</div>' } },
            { path: '/match/:id', name: 'match-details', component: { template: '<div>Match</div>' } },
            { path: '/tournament/:id', name: 'tournament-details', component: { template: '<div>Tournament</div>' } }
        ]
    })
}

const createTestI18n = () => {
    return createI18n({
        legacy: false,
        locale: 'en',
        messages: {
            en: {
                tournament: {
                    ongoing: 'Ongoing',
                    ended: 'Ended',
                    rounds: 'Rounds',
                    round: 'Round',
                    past: 'Past',
                    future: 'Future',
                    missing: 'Missing',
                    registered: 'Registered'
                }
            }
        }
    })
}

describe('Component Type Compatibility Tests', () => {
    beforeEach(() => {
        setActivePinia(createPinia())
    })

    it('should accept ExtendedMatch array in SimpleMatchesList component', () => {
        const router = createTestRouter()
        const i18n = createTestI18n()

        // Create mock ExtendedMatch objects
        const mockMatches: ExtendedMatch[] = [
            {
                id: 1,
                name: 'Test Match 1',
                organizerId: 1,
                isActive: true,
                startDate: '2024-01-01',
                endDate: '2024-01-01',
                city: 'Test City',
                country: 'Test Country',
                competitionLevel: 'National',
                // Extended properties
                distanceKm: 10,
                sunriseTime: '06:00',
                sunsetTime: '18:00',
                temperatureCelsius: 20,
                participantProgress: 75,
                participantsCount: 50
            },
            {
                id: 2,
                name: 'Test Match 2',
                organizerId: 1,
                isActive: true,
                startDate: '2024-01-02',
                endDate: '2024-01-02',
                city: 'Another City',
                country: 'Another Country',
                competitionLevel: 'International',
                // Extended properties
                distanceKm: 15,
                sunriseTime: '06:30',
                sunsetTime: '17:30',
                temperatureCelsius: 18,
                participantProgress: 60,
                participantsCount: 75
            }
        ]

        // Mount component with ExtendedMatch array
        const wrapper = mount(MockSimpleMatchesList, {
            props: {
                items: mockMatches
            },
            global: {
                plugins: [router, i18n]
            }
        })

        // Verify component accepts the props correctly
        const items = wrapper.props('items') as ExtendedMatch[]
        expect(items).toHaveLength(2)
        expect(items[0]?.name).toBe('Test Match 1')
        expect(items[0]?.distanceKm).toBe(10)
        expect(items[1]?.name).toBe('Test Match 2')
        expect(items[1]?.distanceKm).toBe(15)

        // Verify component renders correctly
        expect(wrapper.find('[data-testid="matches-list"]').text()).toBe('2 matches')
    })

    it('should accept TournamentWithMatches in TournamentItem component', () => {
        const router = createTestRouter()
        const i18n = createTestI18n()

        // Create mock ExtendedMatch objects for the tournament
        const mockMatches: ExtendedMatch[] = [
            {
                id: 1,
                name: 'Tournament Match 1',
                organizerId: 1,
                isActive: true,
                startDate: '2024-01-01',
                endDate: '2024-01-01',
                distanceKm: 5
            },
            {
                id: 2,
                name: 'Tournament Match 2',
                organizerId: 1,
                isActive: true,
                startDate: '2024-01-02',
                endDate: '2024-01-02',
                distanceKm: 10
            }
        ]

        // Create mock TournamentWithMatches object
        const mockTournament: TournamentWithMatches = {
            id: 1,
            name: 'Test Tournament',
            organizerId: 1,
            description: 'A test tournament',
            totalRounds: 2,
            matches: mockMatches
        }

        // Mount component with TournamentWithMatches object
        const wrapper = mount(MockTournamentItem, {
            props: {
                tournament: mockTournament,
                isSelected: false
            },
            global: {
                plugins: [router, i18n]
            }
        })

        // Verify component accepts the props correctly
        const tournament = wrapper.props('tournament') as TournamentWithMatches
        expect(tournament?.name).toBe('Test Tournament')
        expect(tournament?.matches).toHaveLength(2)
        expect(tournament?.matches?.[0]?.name).toBe('Tournament Match 1')
        expect(tournament?.matches?.[1]?.name).toBe('Tournament Match 2')
        expect(wrapper.props('isSelected')).toBe(false)

        // Verify component renders correctly
        expect(wrapper.find('[data-testid="tournament-item"]').text()).toBe('Test Tournament')
    })

    it('should handle optional properties in ExtendedMatch correctly', () => {
        const router = createTestRouter()
        const i18n = createTestI18n()

        // Create ExtendedMatch with minimal required properties
        const minimalMatch: ExtendedMatch = {
            id: 1,
            name: 'Minimal Match',
            organizerId: 1,
            isActive: true
            // All other properties are optional
        }

        // Create ExtendedMatch with all optional properties
        const fullMatch: ExtendedMatch = {
            id: 2,
            name: 'Full Match',
            organizerId: 1,
            isActive: true,
            startDate: '2024-01-01',
            endDate: '2024-01-01',
            city: 'Test City',
            country: 'Test Country',
            description: 'A full match with all properties',
            competitionLevel: 'National',
            international: true,
            distanceKm: 10,
            sunriseTime: '06:00',
            sunsetTime: '18:00',
            temperatureCelsius: 20,
            participantProgress: 75,
            participantsCount: 50,
            payments: { registration: 50, equipment: 25 },
            ageCategories: [
                { id: 1, name: 'Senior', min_age: 18, max_age: 65 }
            ],
            equipmentCategories: [
                { id: 1, name: 'Recurve Bow', description: 'Traditional recurve bow' }
            ]
        }

        const matches = [minimalMatch, fullMatch]

        // Mount component with mixed ExtendedMatch objects
        const wrapper = mount(MockSimpleMatchesList, {
            props: {
                items: matches
            },
            global: {
                plugins: [router, i18n]
            }
        })

        // Verify both minimal and full matches are accepted
        const items = wrapper.props('items') as ExtendedMatch[]
        expect(items).toHaveLength(2)
        expect(items[0]?.name).toBe('Minimal Match')
        expect(items[0]?.distanceKm).toBeUndefined()
        expect(items[1]?.name).toBe('Full Match')
        expect(items[1]?.distanceKm).toBe(10)
        expect(items[1]?.payments).toEqual({ registration: 50, equipment: 25 })
        expect(items[1]?.ageCategories).toHaveLength(1)
    })

    it('should handle optional matches property in TournamentWithMatches', () => {
        const router = createTestRouter()
        const i18n = createTestI18n()

        // Create TournamentWithMatches without matches
        const tournamentWithoutMatches: TournamentWithMatches = {
            id: 1,
            name: 'Tournament Without Matches',
            organizerId: 1
            // matches property is optional
        }

        // Create TournamentWithMatches with empty matches array
        const tournamentWithEmptyMatches: TournamentWithMatches = {
            id: 2,
            name: 'Tournament With Empty Matches',
            organizerId: 1,
            matches: []
        }

        // Test tournament without matches
        const wrapper1 = mount(MockTournamentItem, {
            props: {
                tournament: tournamentWithoutMatches
            },
            global: {
                plugins: [router, i18n]
            }
        })

        const tournament1 = wrapper1.props('tournament') as TournamentWithMatches
        expect(tournament1?.matches).toBeUndefined()
        expect(wrapper1.find('[data-testid="tournament-item"]').text()).toBe('Tournament Without Matches')

        // Test tournament with empty matches
        const wrapper2 = mount(MockTournamentItem, {
            props: {
                tournament: tournamentWithEmptyMatches
            },
            global: {
                plugins: [router, i18n]
            }
        })

        const tournament2 = wrapper2.props('tournament') as TournamentWithMatches
        expect(tournament2?.matches).toEqual([])
        expect(wrapper2.find('[data-testid="tournament-item"]').text()).toBe('Tournament With Empty Matches')
    })

    it('should properly type JSON field interfaces in component props', () => {
        // Test that components can receive and work with JSON field interfaces
        const mockMatch: ExtendedMatch = {
            id: 1,
            name: 'Match with JSON Fields',
            organizerId: 1,
            isActive: true,
            payments: { registration: 50.00, equipment: 25.00 },
            ageCategories: [
                {
                    id: 1,
                    name: 'Senior',
                    short_name: 'SR',
                    abbreviation: 'S',
                    min_age: 18,
                    max_age: 65
                },
                {
                    id: 2,
                    name: 'Junior',
                    short_name: 'JR',
                    abbreviation: 'J',
                    min_age: 12,
                    max_age: 17
                }
            ],
            equipmentCategories: [
                {
                    id: 1,
                    name: 'Recurve Bow',
                    description: 'Traditional recurve bow',
                    requirements: ['bow', 'arrows', 'string']
                },
                {
                    id: 2,
                    name: 'Compound Bow',
                    description: 'Modern compound bow',
                    requirements: ['bow', 'arrows', 'release aid']
                }
            ]
        }

        // Verify that the JSON field interfaces are properly typed
        expect(mockMatch.payments).toEqual({ registration: 50.00, equipment: 25.00 })

        expect(mockMatch.ageCategories).toHaveLength(2)
        expect(mockMatch.ageCategories?.[0].name).toBe('Senior')
        expect(mockMatch.ageCategories?.[0].min_age).toBe(18)

        expect(mockMatch.equipmentCategories).toHaveLength(2)
        expect(mockMatch.equipmentCategories?.[0].name).toBe('Recurve Bow')
        expect(mockMatch.equipmentCategories?.[0].requirements).toContain('bow')
    })

    it('should handle component emits with proper typing', () => {
        const router = createTestRouter()
        const i18n = createTestI18n()

        const mockTournament: TournamentWithMatches = {
            id: 1,
            name: 'Test Tournament',
            organizerId: 1
        }

        const wrapper = mount(MockTournamentItem, {
            props: {
                tournament: mockTournament
            },
            global: {
                plugins: [router, i18n]
            }
        })

        // Test that emits are properly typed (this is more of a compilation test)
        // The component should be able to emit with the correct types
        expect(wrapper.emitted()).toEqual({})

        // Simulate emit events to verify typing
        wrapper.vm.$emit('select', 1)
        wrapper.vm.$emit('hover', 1)
        wrapper.vm.$emit('hover', null)

        expect(wrapper.emitted('select')).toEqual([[1]])
        expect(wrapper.emitted('hover')).toEqual([[1], [null]])
    })
})
