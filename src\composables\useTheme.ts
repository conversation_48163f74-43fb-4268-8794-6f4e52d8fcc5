import { ref, watch, onMounted } from 'vue'
import { useStorage } from '@vueuse/core'

export type Theme = 'light' | 'dark' | 'system'

const THEME_STORAGE_KEY = 'ap-theme'

export function useTheme() {
  // Store the theme preference in localStorage
  const storedTheme = useStorage<Theme>(THEME_STORAGE_KEY, 'system')
  
  // Current applied theme (resolved from system if needed)
  const currentTheme = ref<'light' | 'dark'>('light')
  
  // Check if system prefers dark mode
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light'
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  
  // Apply theme to document
  const applyTheme = (theme: 'light' | 'dark') => {
    if (typeof document === 'undefined') return
    
    const root = document.documentElement
    
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    currentTheme.value = theme
  }
  
  // Resolve theme based on preference
  const resolveTheme = (themePreference: Theme): 'light' | 'dark' => {
    if (themePreference === 'system') {
      return getSystemTheme()
    }
    return themePreference
  }
  
  // Set theme preference
  const setTheme = (theme: Theme) => {
    storedTheme.value = theme
    const resolvedTheme = resolveTheme(theme)
    applyTheme(resolvedTheme)
  }
  
  // Force dark mode (for organizer mode)
  const forceDarkMode = () => {
    applyTheme('dark')
  }
  
  // Restore theme from preference
  const restoreTheme = () => {
    const resolvedTheme = resolveTheme(storedTheme.value)
    applyTheme(resolvedTheme)
  }
  
  // Watch for system theme changes
  const watchSystemTheme = () => {
    if (typeof window === 'undefined') return
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      if (storedTheme.value === 'system') {
        const systemTheme = getSystemTheme()
        applyTheme(systemTheme)
      }
    }
    
    mediaQuery.addEventListener('change', handleChange)
    
    // Return cleanup function
    return () => mediaQuery.removeEventListener('change', handleChange)
  }
  
  // Initialize theme on mount
  onMounted(() => {
    restoreTheme()
    const cleanup = watchSystemTheme()
    
    // Cleanup on unmount
    return cleanup
  })
  
  // Watch for theme preference changes
  watch(storedTheme, (newTheme) => {
    const resolvedTheme = resolveTheme(newTheme)
    applyTheme(resolvedTheme)
  })
  
  return {
    theme: storedTheme,
    currentTheme,
    setTheme,
    forceDarkMode,
    restoreTheme,
    isDark: () => currentTheme.value === 'dark'
  }
}
