// Centralized type exports for the Archery Points application
// This file provides a single import point for all application types

// Import and re-export feathers client augmentations
// This ensures the module augmentations are loaded
import './feathers-augmentation'

// Re-export base feathers client types
export type {
    Match, MatchData, MatchQuery, MatchPatch,
    Tournament, TournamentData, TournamentQuery, TournamentPatch,
    Player, PlayerData, PlayerQuery, PlayerPatch,
    Equipment, EquipmentData, EquipmentQuery, EquipmentPatch,
    Club, ClubData, ClubQuery, ClubPatch,
    Organizer, OrganizerData, OrganizerQuery, OrganizerPatch,
    User, UserData, UserQuery, UserPatch,
    Federation, FederationData, FederationQuery, FederationPatch,
    MatchResult, MatchResultData, MatchResultQuery, MatchResultPatch,
    MatchRegistration, MatchRegistrationData, MatchRegistrationQuery, MatchRegistrationPatch,
    Message, MessageData, MessageQuery, MessagePatch
} from '@/api/feathers-client'

// Re-export match-related types
export type {
    ExtendedMatch,
    ExtendedMatchOptionalFields,
    AgeCategory,
    PaymentEntry,
    EquipmentCategory
} from './match'

// Re-export tournament-related types
export type {
    TournamentWithMatches
} from './tournament'

// Re-export display-specific types
export type {
    DisplayEquipmentCategory,
    DisplayAgeCategory,
    DisplayStyleCategory,
    DisplayDataType
} from './display'
