<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useMatchesService } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { Badge } from '@/components/ui/badge'
import type { Match, MatchResult } from '@/api/feathers-client'

const props = defineProps<{
  match: Match | null | undefined
  compact?: boolean // For smaller display in MatchDetailsWidget
}>()

const matchesService = useMatchesService()
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

const playerResult = ref<MatchResult | null>(null)
const isLoadingResult = ref(false)

// Check if match is in the past
const isMatchInPast = computed(() => {
  if (!props.match?.endDate) return false
  return new Date(props.match.endDate) < new Date()
})

async function fetchPlayerResult() {
  if (!props.match?.id || !activePlayer.value) {
    playerResult.value = null
    return
  }

  // Only check for results if match is in the past
  if (!isMatchInPast.value) {
    playerResult.value = null
    return
  }

  isLoadingResult.value = true
  try {
    const results = await matchesService.findMatchResults({
      query: {
        matchId: props.match.id,
        playerId: activePlayer.value.id,
        $populate: 'player'
      }
    })

    playerResult.value = results.find(
      result => result.matchId === props.match?.id && result.playerId === activePlayer.value!.id
    ) || null
  } catch (err) {
    console.error('Failed to fetch player result:', err)
    playerResult.value = null
  } finally {
    isLoadingResult.value = false
  }
}

// Watch for changes in match or activePlayer
watch([() => props.match, activePlayer], () => {
  fetchPlayerResult()
}, { immediate: true })

onMounted(() => {
  fetchPlayerResult()
})
</script>

<template>
  <div v-if="playerResult && isMatchInPast" :class="[
    'p-3 bg-primary/10 border border-primary/20 rounded-lg',
    compact ? 'p-2' : 'p-4'
  ]">
    <div class="flex items-center justify-between">
      <div>
        <p :class="[
          'font-semibold text-primary mb-2',
          compact ? 'text-sm mb-1' : ''
        ]">Your Result</p>
        <div class="flex gap-1">
          <Badge
            v-if="playerResult.styleDivision"
            variant="default"
            :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
          >
            {{ playerResult.styleDivision }}
          </Badge>
          <Badge
            v-if="playerResult.ageDivision"
            variant="secondary"
            :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
          >
            {{ playerResult.ageDivision }}
          </Badge>
          <Badge
            v-if="playerResult.genderDivision"
            variant="outline"
            :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
          >
            {{ playerResult.genderDivision }}
          </Badge>
        </div>
      </div>
      <div class="text-right">
        <p :class="[
          'font-bold text-primary',
          compact ? 'text-lg' : 'text-xl'
        ]">{{ playerResult.points }}/{{ playerResult.maxPoints }}</p>
        <p v-if="playerResult.place" :class="[
          'text-muted-foreground',
          compact ? 'text-xs' : 'text-sm'
        ]">Place: {{ playerResult.place }}</p>
      </div>
    </div>
  </div>
</template>
