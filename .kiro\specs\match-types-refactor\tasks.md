# Implementation Plan

- [x] 1. Create consolidated type files structure
  - Create new type files with proper TypeScript interfaces and module augmentations
  - Establish the foundation for the new type hierarchy
  - _Requirements: 1.1, 2.2, 6.3_

- [x] 1.1 Create feathers client augmentation file
  - Write `src/types/feathers-augmentation.ts` with consolidated module augmentations for Match, MatchData, and Player interfaces
  - Move all augmentations from `global.d.ts` and `extended-match-data.d.ts` into single file
  - _Requirements: 1.1, 2.1, 2.2_

- [x] 1.2 Create match types file
  - Write `src/types/match.ts` with ExtendedMatch type and related interfaces
  - Define proper TypeScript interfaces for AgeCategory, PaymentEntry, and EquipmentCategory
  - _Requirements: 3.1, 3.2, 3.4, 6.2_

- [x] 1.3 Create tournament types file
  - Write `src/types/tournament.ts` with TournamentWithMatches interface
  - Consolidate tournament-related type extensions
  - _Requirements: 5.1, 5.5_

- [x] 1.4 Create display types file
  - Write `src/types/display.ts` with DisplayEquipmentCategory and other display-specific interfaces
  - Move presentation logic types from components
  - _Requirements: 5.3, 6.2_

- [x] 1.5 Create types index file
  - Write `src/types/index.ts` to re-export all types with clear organization
  - Provide centralized import point for all application types
  - _Requirements: 6.3, 6.4_

- [x] 2. Update component imports and remove local type definitions
  - Refactor all components to use centralized type definitions
  - Remove duplicate local interface definitions
  - _Requirements: 4.1, 4.2, 5.2, 5.4_

- [x] 2.1 Update tournament components
  - Refactor `TournamentDetailsWidget.vue`, `TournamentsContainer.vue`, `TournamentsList.vue`, and `TournamentItem.vue`
  - Replace local TournamentWithMatches definitions with imports from `@/types/tournament`
  - _Requirements: 5.1, 5.4, 4.2_

- [x] 2.2 Update match detail component
  - Refactor `MatchDetailsWidget.vue` to remove local ExtendedMatchOptionalFields and DisplayEquipmentCategory interfaces
  - Update imports to use centralized type definitions from `@/types`
  - _Requirements: 5.2, 5.3, 4.2_

- [x] 2.3 Update other match components
  - Refactor `MatchesList.vue`, `MatchesContainer.vue` and other match-related components
  - Update type imports to use consistent centralized definitions
  - _Requirements: 5.4, 4.2_

- [x] 2.4 Update view components
  - Refactor view components (`TournamentDetailsView.vue`, `OrganizerDetailsView.vue`, `MyMatchesView.vue`, `MatchDetailsView.vue`)
  - Update imports to use consistent type definitions from feathers client and centralized types
  - _Requirements: 4.2, 4.3_

- [x] 3. Remove old type definition files
  - Clean up duplicate and obsolete type definition files
  - Ensure no breaking changes to existing functionality
  - _Requirements: 2.1, 2.3, 4.3_

- [x] 3.1 Remove global.d.ts file
  - Delete `src/types/global.d.ts` since augmentations are moved to feathers-augmentation.ts
  - Verify no components are importing from this file
  - _Requirements: 2.1, 2.3_

- [x] 3.2 Remove extended-match-data.d.ts file
  - Delete `src/types/extended-match-data.d.ts` since augmentations are consolidated
  - Verify no components are importing from this file
  - _Requirements: 2.1, 2.3_

- [x] 3.3 Remove old match.d.ts file
  - Delete `src/types/match.d.ts` since it's replaced by match.ts
  - Verify all imports are updated to use new match.ts file
  - _Requirements: 2.3, 4.3_

- [x] 4. Update composables and stores
  - Update any composables or stores that use Match types
  - Ensure consistent type usage across the application
  - _Requirements: 4.1, 4.2_

- [x] 4.1 Update usePlayerDivisions composable
  - Refactor `src/composables/usePlayerDivisions.ts` to import ExtendedMatch from centralized types
  - Verify the composable works correctly with the new type definitions
  - _Requirements: 4.1, 4.2_

- [x] 4.2 Verify stores compatibility
  - Check `src/stores/matches.ts` and other stores for compatibility with new type structure
  - Update any type imports if necessary to maintain consistency
  - _Requirements: 4.1, 4.2_

- [x] 5. Add comprehensive type tests
  - Create tests to verify type compilation and compatibility
  - Ensure no TypeScript errors and proper type inference
  - _Requirements: 1.3, 2.1_

- [x] 5.1 Create type compilation test
  - Write test file to verify all new types compile correctly without errors
  - Test import resolution and module augmentation functionality
  - _Requirements: 1.3, 2.1_

- [x] 5.2 Create component type compatibility test
  - Write tests to verify components accept new type definitions correctly
  - Test props validation and type inference in component usage
  - _Requirements: 4.1, 4.2_
