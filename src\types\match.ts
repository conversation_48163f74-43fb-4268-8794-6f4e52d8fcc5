import type { Match } from '@/api/feathers-client'

// Age category structure for JSON fields
export interface AgeCategory {
  id: string | number
  name?: string
  short_name?: string
  abbreviation?: string
  min_age?: number
  min?: number
  max_age?: number
  max?: number
}

// Payment entry structure for JSON fields
export interface PaymentEntry {
  amount: number
  currency: string
  category?: string
  description?: string
}

// Equipment category structure for JSON fields
export interface EquipmentCategory {
  id: string | number
  name: string
  short_name?: string
  description?: string
  requirements?: string[]
}

// Extended match interface with computed fields
export interface ExtendedMatchOptionalFields {
  // Computed fields for display and calculations
  distanceKm?: number
  sunriseTime?: string
  sunsetTime?: string
  temperatureCelsius?: number
  participantProgress?: number
  participantsCount?: number

  // Legacy fields for backward compatibility
  competitionLevel?: string
  international?: boolean
  matchType?: string
  competition_level?: string
  is_international?: boolean
  match_type?: string
  participants_count?: number

  // Application-specific stored fields (previously in module augmentation)
  isActive?: boolean
  withoutLimits?: boolean
  licenseRequired?: boolean
  judges?: unknown // Support any format from API, will be handled in components
  agenda?: unknown // Support any format from API, will be handled in components
  payments?: unknown // Support any format from API, will be handled in components
  ageDivisions?: unknown // Support any format from API, will be handled in components
  styleDivisions?: unknown // Support any format from API, will be handled in components

  // Structured data fields
  ageCategories?: AgeCategory[]
  equipmentCategories?: EquipmentCategory[]
}

// Main extended match type combining base Match with computed fields
export type ExtendedMatch = Match & ExtendedMatchOptionalFields
