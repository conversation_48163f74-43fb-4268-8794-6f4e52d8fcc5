<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useTournamentsService } from '@/stores/tournaments'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Calendar, Users, Trophy, AlertCircle, Plus, Building2 } from 'lucide-vue-next'
import type { Tournament } from '@/api/feathers-client'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const tournamentsService = useTournamentsService()
const userStore = useUserStore()
const { activeOrganizer } = storeToRefs(userStore)

const isLoading = ref(false)
const error = ref<Error | null>(null)
const organizerTournaments = ref<Tournament[]>([])

// Fetch tournaments for the active organizer
const fetchOrganizerTournaments = async () => {
  if (!activeOrganizer.value?.id) {
    console.warn('No active organizer found')
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const response = await tournamentsService.findTournaments({
      query: {
        organizerId: activeOrganizer.value.id,
        $limit: 100,
        $sort: { createdAt: -1 }
      }
    })
    organizerTournaments.value = response || []
  } catch (err) {
    console.error('Failed to fetch organizer tournaments:', err)
    error.value = err instanceof Error ? err : new Error('Failed to fetch tournaments')
  } finally {
    isLoading.value = false
  }
}

// Group tournaments by status
const upcomingTournaments = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return organizerTournaments.value.filter(tournament => {
    if (!tournament.createdAt) return false
    const createdDate = new Date(tournament.createdAt)
    return createdDate >= today
  })
})

const pastTournaments = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return organizerTournaments.value.filter(tournament => {
    if (!tournament.createdAt) return false
    const createdDate = new Date(tournament.createdAt)
    return createdDate < today
  })
})

const goToTournament = (tournamentId: number) => {
  router.push({ name: 'tournament-details', params: { id: tournamentId.toString() } })
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

// Watch for changes in activeOrganizer and fetch tournaments when it becomes available
onMounted(async () => {
  if (activeOrganizer.value) {
    await fetchOrganizerTournaments()
  }
})
</script>

<template>
  <SidebarProvider>
    <SidebarInset>
      <div class="flex flex-col gap-4 p-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold">{{ t('navigation.organizerTournaments') }}</h1>
            <p v-if="activeOrganizer" class="text-muted-foreground">
              {{ activeOrganizer.name }}
            </p>
          </div>
          <Button @click="router.push('/tournaments')" variant="outline" class="flex items-center gap-2">
            <Building2 class="h-4 w-4" />
            Browse All Tournaments
          </Button>
        </div>

        <div v-if="!activeOrganizer" class="text-center p-8">
          <AlertCircle class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 class="text-lg font-medium mb-2">No Organizer Selected</h3>
          <p class="text-muted-foreground">Please select an organizer profile to view tournaments.</p>
        </div>

        <div v-else-if="isLoading" class="text-center p-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="mt-4 text-muted-foreground">Loading tournaments...</p>
        </div>

        <div v-else-if="error" class="text-center p-8">
          <AlertCircle class="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 class="text-lg font-medium mb-2 text-red-600">Error Loading Tournaments</h3>
          <p class="text-muted-foreground mb-4">{{ error.message }}</p>
          <Button @click="fetchOrganizerTournaments" variant="outline">
            Try Again
          </Button>
        </div>

        <div v-else class="grid gap-6">
          <!-- Summary Cards -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Total Tournaments</CardTitle>
                <Trophy class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ organizerTournaments.length }}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Upcoming</CardTitle>
                <Calendar class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ upcomingTournaments.length }}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Completed</CardTitle>
                <Building2 class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ pastTournaments.length }}</div>
              </CardContent>
            </Card>
          </div>

          <!-- Tournaments Lists -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Upcoming Tournaments -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Calendar class="h-5 w-5" />
                  Upcoming Tournaments
                  <Badge variant="secondary">{{ upcomingTournaments.length }}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="upcomingTournaments.length > 0" class="space-y-3">
                  <div
                    v-for="tournament in upcomingTournaments"
                    :key="tournament.id"
                    @click="goToTournament(tournament.id)"
                    class="p-3 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
                  >
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h4 class="font-medium">{{ tournament.name }}</h4>
                        <p class="text-sm text-muted-foreground mt-1">
                          {{ formatDate(tournament.createdAt) }}
                          <span v-if="tournament.description">· {{ tournament.description }}</span>
                        </p>
                      </div>
                      <Badge variant="outline" class="ml-2">
                        Active: {{ tournament.isActive ? 'Yes' : 'No' }}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-muted-foreground">
                  <Calendar class="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No upcoming tournaments</p>
                </div>
              </CardContent>
            </Card>

            <!-- Past Tournaments -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Trophy class="h-5 w-5" />
                  Past Tournaments
                  <Badge variant="secondary">{{ pastTournaments.length }}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="pastTournaments.length > 0" class="space-y-3">
                  <div
                    v-for="tournament in pastTournaments"
                    :key="tournament.id"
                    @click="goToTournament(tournament.id)"
                    class="p-3 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
                  >
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h4 class="font-medium">{{ tournament.name }}</h4>
                        <p class="text-sm text-muted-foreground mt-1">
                          {{ formatDate(tournament.createdAt) }}
                          <span v-if="tournament.description">· {{ tournament.description }}</span>
                        </p>
                      </div>
                      <Badge variant="outline" class="ml-2">
                        Active: {{ tournament.isActive ? 'Yes' : 'No' }}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-muted-foreground">
                  <Trophy class="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No past tournaments</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
