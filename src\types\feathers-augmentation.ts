// Consolidated module augmentations for feathers client types
// This file consolidates all augmentations from global.d.ts and extended-match-data.d.ts
//
// Note: Module augmentation is currently disabled due to type conflicts
// The extended types are now handled through intersection types in match.ts
//
// This file serves as a placeholder for future module augmentation needs
// and ensures the import in index.ts doesn't break

export { }
