<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

const { t } = useI18n()
const router = useRouter()

defineProps<{
  canGoBack: boolean
  canProceed: boolean
  isLastStep: boolean
  isSubmitting: boolean
}>()

const emit = defineEmits<{
  (e: 'previous-step'): void
  (e: 'next-step'): void
  (e: 'submit-form'): void
}>()
</script>

<template>
  <div class="flex justify-between mt-8">
    <Button @click="emit('previous-step')" variant="outline" :disabled="!canGoBack">
      <ChevronLeft class="w-4 h-4 mr-2" />
      {{ t('common.previous') }}
    </Button>

    <div class="flex gap-2">
      <Button @click="router.push({ name: 'matches' })" variant="ghost">
        {{ t('common.cancel') }}
      </Button>

      <Button v-if="!isLastStep" @click="emit('next-step')" :disabled="!canProceed">
        {{ t('common.next') }}
        <ChevronRight class="w-4 h-4 ml-2" />
      </Button>

      <Button v-else @click="emit('submit-form')" :disabled="!canProceed || isSubmitting">
        {{ isSubmitting ? t('common.loading') : t('common.save') }}
      </Button>
    </div>
  </div>
</template>
