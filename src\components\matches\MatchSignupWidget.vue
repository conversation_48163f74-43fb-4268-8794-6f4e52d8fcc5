<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useMatchesService } from '@/stores/matches'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { Switch } from '@/components/ui/switch'
import type { ExtendedMatch } from '@/types/match'

const props = defineProps<{
  match: ExtendedMatch | null | undefined,
  showLabel?: boolean
}>()

const { t } = useI18n()
const matchesService = useMatchesService()
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

const isSignedUp = ref(false)
const isSignupLoading = ref(false)

const currentMatch = computed(() => props.match)

// Check if match is in the past
const isMatchInPast = computed(() => {
  if (!currentMatch.value?.endDate) return false
  return new Date(currentMatch.value.endDate) < new Date()
})

// Check if registration is closed
const isRegistrationClosed = computed(() => {
  if (!currentMatch.value?.registrationEnds) return false
  return new Date(currentMatch.value.registrationEnds) < new Date()
})

// Check if signup should be disabled
const isSignupDisabled = computed(() => {
  return isMatchInPast.value || isRegistrationClosed.value || isSignupLoading.value || !activePlayer.value
})

async function registerForMatch() {
  console.log('🔄 registerForMatch() called')

  if (!currentMatch.value || !activePlayer.value) {
    console.error('❌ Missing match or player data in registerForMatch')
    console.log('  - currentMatch:', currentMatch.value)
    console.log('  - activePlayer:', activePlayer.value)
    return
  }

  const eq = currentMatch.value.equipmentCategories?.[0]
  // Handle optional short_name property with proper fallback values
  const styleDivision = typeof eq === 'string'
    ? eq
    : (eq?.short_name ?? eq?.name ?? 'recurve');

  const age = currentMatch.value.ageCategories?.[0]
  // Handle optional short_name property with proper fallback values
  const ageDivision = typeof age === 'string'
    ? age
    : (age?.short_name ?? age?.name ?? 'senior');

  console.log('📋 Equipment categories:', currentMatch.value.equipmentCategories);
  console.log('🎯 Selected style division:', styleDivision);
  console.log('📋 Age categories:', currentMatch.value.ageCategories);
  console.log('🎯 Selected age division:', ageDivision);

  const registrationData = {
    matchId: currentMatch.value.id,
    playerId: activePlayer.value.id,
    styleDivision: String(styleDivision),
    ageDivision: String(ageDivision),
    genderDivision: String(activePlayer.value.sex || 'M')
  }

  console.log('📋 Registration data prepared:', registrationData)
  console.log('🌐 Calling matchesService.createMatchRegistration...')

  try {
    const result = await matchesService.createMatchRegistration(registrationData)
    console.log('✅ API call successful! Registration created:', result)
    return result
  } catch (error) {
    console.error('❌ API call failed:', error)
    throw error
  }
}

async function unregisterFromMatch() {
  console.log('🔄 unregisterFromMatch() called')

  if (!currentMatch.value || !activePlayer.value) {
    console.error('❌ Missing match or player data in unregisterFromMatch')
    return
  }

  console.log('🔍 Looking for existing registration...')
  try {
    const registrations = await matchesService.findMatchRegistrations({
      query: {
        matchId: currentMatch.value.id,
        playerId: activePlayer.value.id
      }
    })

    const existingRegistration = registrations.find(
      reg => reg.matchId === currentMatch.value!.id && reg.playerId === activePlayer.value!.id
    )

    if (!existingRegistration) {
      console.error('❌ No existing registration found to remove')
      return
    }

    console.log('📋 Found registration to remove:', existingRegistration)
    console.log('🌐 Calling matchesService.removeMatchRegistration...')

    await matchesService.removeMatchRegistration(existingRegistration.id)
    console.log('✅ Registration removed successfully!')
  } catch (error) {
    console.error('❌ Failed to remove registration:', error)
    throw error
  }
}

async function checkSignupStatus() {
  console.log('Checking signup status...')
  console.log('Current match:', currentMatch.value?.id)
  console.log('Active player:', activePlayer.value?.id)

  if (!currentMatch.value || !activePlayer.value) {
    console.log('Missing match or player, setting isSignedUp to false')
    isSignedUp.value = false
    return
  }

  try {
    console.log('Fetching match registrations...')
    const registrations = await matchesService.findMatchRegistrations({
      query: {
        matchId: currentMatch.value.id,
        playerId: activePlayer.value.id
      }
    })

    console.log('Match registrations:', registrations)

    const existingRegistration = registrations.find(
      reg => reg.matchId === currentMatch.value!.id && reg.playerId === activePlayer.value!.id
    )

    console.log('Existing registration found:', existingRegistration)
    isSignedUp.value = !!existingRegistration
    console.log('isSignedUp set to:', isSignedUp.value)
  } catch (error) {
    console.error('Error checking signup status:', error)
    isSignedUp.value = false
  }
}

watch([currentMatch, activePlayer], () => {
  if (currentMatch.value && activePlayer.value) {
    checkSignupStatus()
  } else {
    isSignedUp.value = false
  }
}, { immediate: true })

async function handleSwitchClick(event: Event) {
  console.log('=== SWITCH CLICKED ===')
  console.log('Event:', event)
  console.log('Current isSignedUp:', isSignedUp.value)
  console.log('Current match:', currentMatch.value?.id)
  console.log('Active player:', activePlayer.value?.id)

  event.preventDefault()
  event.stopPropagation()

  if (!currentMatch.value || !activePlayer.value) {
    console.error('❌ No match or player available for signup')
    return
  }

  if (isSignupDisabled.value) {
    console.log('🚫 Signup is disabled, ignoring click')
    return
  }

  console.log('🚀 Starting signup process...')
  isSignupLoading.value = true

  try {
    if (isSignedUp.value) {
      console.log('📤 Attempting to unregister...')
      await unregisterFromMatch()
      console.log('✅ Unregistration successful, setting isSignedUp to false')
      isSignedUp.value = false
    } else {
      console.log('📥 Attempting to register...')
      await registerForMatch()
      console.log('✅ Registration successful, setting isSignedUp to true')
      isSignedUp.value = true
    }
  } catch (error) {
    console.error('❌ Error during signup process:', error)
  } finally {
    isSignupLoading.value = false
    console.log('🏁 Signup process completed')
  }
}
</script>

<template>
  <div class="flex items-center space-x-2">
    <Switch
      id="signup-toggle"
      v-model="isSignedUp"
      :disabled="isSignupDisabled"
      @click="handleSwitchClick"
      :class="[
        'data-[state=checked]:bg-green-600',
        'data-[state=unchecked]:bg-red-300',
        isSignupLoading ? ' bg-orange-400  ring-2 ring-orange-300' : '',
        isSignupDisabled && !isSignupLoading ? 'opacity-50 cursor-not-allowed' : ''
      ]"
    />
    <label v-if="showLabel" for="signup-toggle" class="font-semibold text-lg" :class="{ 'text-muted-foreground': isSignupDisabled && !isSignupLoading }">
      <span v-if="isSignupLoading">{{ t('matches.loading') }}</span>
      <span v-else-if="isMatchInPast">{{ t('matches.matchEnded') }}</span>
      <span v-else-if="isRegistrationClosed">{{ t('matches.registrationClosed') }}</span>
      <span v-else>{{ isSignedUp ? t('matches.withdraw') : t('matches.signUp') }}</span>
    </label>
  </div>
</template>
