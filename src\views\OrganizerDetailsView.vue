<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { api } from '@/api/feathers-client'
import type { Organizer, Match } from '@/api/feathers-client'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, MapPin, Phone, Mail, User, Building, CreditCard, Hash } from 'lucide-vue-next'
import SimpleMatchesList from '@/components/matches/SimpleMatchesList.vue'

const route = useRoute()
const router = useRouter()

const organizerId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

const organizer = ref<Organizer | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)

const matches = ref<Match[]>([])
const isLoadingMatches = ref(false)
const errorMatches = ref<Error | null>(null)

const fetchOrganizer = async (id: number) => {
  isLoading.value = true
  error.value = null
  try {
    organizer.value = await api.organizers.get(id)
  } catch (err) {
    if (err instanceof Error) {
      error.value = err
    } else {
      error.value = new Error('Failed to fetch organizer details')
    }
    console.error('Failed to fetch organizer:', err)
  } finally {
    isLoading.value = false
  }
}

const fetchMatches = async (id: number) => {
  isLoadingMatches.value = true
  errorMatches.value = null
  try {
    const res = await api.matches.find({
      query: {
        organizerId: id,
        $limit: 100
      }
    })
    // Sort by startDate descending (upcoming first)
    matches.value = (res.data || []).slice().sort((a, b) => {
      if (!a.startDate) return 1
      if (!b.startDate) return -1
      return new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    })
  } catch (err) {
    if (err instanceof Error) {
      errorMatches.value = err
    } else {
      errorMatches.value = new Error('Failed to fetch matches')
    }
    console.error('Failed to fetch matches:', err)
  } finally {
    isLoadingMatches.value = false
  }
}

onMounted(async () => {
  if (organizerId.value) {
    await fetchOrganizer(organizerId.value)
    await fetchMatches(organizerId.value)
  }
})

const goBack = () => {
  router.back()
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('pl-PL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const upcomingMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return matches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate >= today
  })
})

const pastMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return matches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate < today
  })
})
</script>

<template>
  <div class="min-h-screen bg-background">
    <div class="container px-4 py-6 ">
      <!-- Header -->
      <div class="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" @click="goBack">
          <ArrowLeft class="h-4 w-4" />
        </Button>
        <h1 class="text-2xl font-bold">Organizer Details</h1>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">Loading organizer details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-red-500 mb-4">Error loading organizer details</p>
          <p class="text-muted-foreground">{{ error.message }}</p>
          <Button variant="outline" @click="goBack" class="mt-4">
            Go Back
          </Button>
        </div>
      </div>

      <!-- Organizer Details -->
      <div v-else-if="organizer" class="space-y-6">
        <!-- Main Info Card -->
        <Card>
          <CardHeader>
            <div class="flex items-start justify-between">
              <div class="flex items-center gap-4">
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Building class="w-8 h-8 text-primary" />
                </div>
                <div>
                  <CardTitle class="text-2xl">{{ organizer.name }}</CardTitle>
                  <div class="flex items-center gap-2 mt-1">
                    <Badge :variant="organizer.isActive ? 'default' : 'secondary'">
                      {{ organizer.isActive ? 'Active' : 'Inactive' }}
                    </Badge>
                    <Badge variant="outline" class="text-xs">
                      ID: {{ organizer.id }}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- About Section -->
            <div v-if="organizer.about">
              <h3 class="font-semibold mb-2">About</h3>
              <p class="text-muted-foreground">{{ organizer.about }}</p>
            </div>

            <!-- Contact Person -->
            <div v-if="organizer.contactPerson">
              <div class="flex items-center gap-2">
                <User class="w-4 h-4 text-muted-foreground" />
                <span class="font-medium">Contact Person:</span>
                <span>{{ organizer.contactPerson }}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Contact Information -->
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Address -->
            <div v-if="organizer.address || organizer.city || organizer.country">
              <div class="flex items-start gap-2">
                <MapPin class="w-4 h-4 text-muted-foreground mt-1" />
                <div>
                  <p class="font-medium">Address</p>
                  <div class="text-muted-foreground">
                    <p v-if="organizer.address">{{ organizer.address }}</p>
                    <p>
                      <span v-if="organizer.zipcode">{{ organizer.zipcode }} </span>
                      <span v-if="organizer.city">{{ organizer.city }}</span>
                      <span v-if="organizer.country">, {{ organizer.country }}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Phone -->
            <div v-if="organizer.phone">
              <div class="flex items-center gap-2">
                <Phone class="w-4 h-4 text-muted-foreground" />
                <span class="font-medium">Phone:</span>
                <a :href="`tel:${organizer.phone}`" class="text-primary hover:underline">
                  {{ organizer.phone }}
                </a>
              </div>
            </div>

            <!-- User Email (if available) -->
            <div v-if="organizer.user?.email">
              <div class="flex items-center gap-2">
                <Mail class="w-4 h-4 text-muted-foreground" />
                <span class="font-medium">Email:</span>
                <a :href="`mailto:${organizer.user.email}`" class="text-primary hover:underline">
                  {{ organizer.user.email }}
                </a>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Business Information -->
        <Card v-if="organizer.bankAccount || organizer.taxId">
          <CardHeader>
            <CardTitle>Business Information</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Tax ID -->
            <div v-if="organizer.taxId">
              <div class="flex items-center gap-2">
                <Hash class="w-4 h-4 text-muted-foreground" />
                <span class="font-medium">Tax ID:</span>
                <span class="font-mono text-sm">{{ organizer.taxId }}</span>
              </div>
            </div>

            <!-- Bank Account -->
            <div v-if="organizer.bankAccount">
              <div class="flex items-center gap-2">
                <CreditCard class="w-4 h-4 text-muted-foreground" />
                <span class="font-medium">Bank Account:</span>
                <span class="font-mono text-sm">{{ organizer.bankAccount }}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- System Information -->
        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-muted-foreground">Created:</span>
                <p>{{ formatDate(organizer.createdAt) }}</p>
              </div>
              <div>
                <span class="font-medium text-muted-foreground">Last Updated:</span>
                <p>{{ formatDate(organizer.updatedAt) }}</p>
              </div>
              <div v-if="organizer.legacyId">
                <span class="font-medium text-muted-foreground">Legacy ID:</span>
                <p>{{ organizer.legacyId }}</p>
              </div>
              <div>
                <span class="font-medium text-muted-foreground">User ID:</span>
                <p>{{ organizer.userId }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Organizer's Matches -->
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Matches</CardTitle>
          </CardHeader>
          <CardContent>
            <div v-if="isLoadingMatches">
              <div class="text-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                <p class="text-muted-foreground">Loading matches...</p>
              </div>
            </div>
            <div v-else-if="errorMatches">
              <div class="text-center text-red-500 py-4">
                Error loading matches: {{ errorMatches.message }}
              </div>
            </div>
            <div v-else>
              <SimpleMatchesList :items="upcomingMatches" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Past Matches</CardTitle>
          </CardHeader>
          <CardContent>
            <div v-if="isLoadingMatches">
              <div class="text-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                <p class="text-muted-foreground">Loading matches...</p>
              </div>
            </div>
            <div v-else-if="errorMatches">
              <div class="text-center text-red-500 py-4">
                Error loading matches: {{ errorMatches.message }}
              </div>
            </div>
            <div v-else>
              <SimpleMatchesList :items="pastMatches" />
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Not Found -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-muted-foreground mb-4">Organizer not found</p>
          <Button variant="outline" @click="goBack">
            Go Back
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
