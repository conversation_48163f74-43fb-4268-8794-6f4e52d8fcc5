<template>
  <div class="border rounded-lg overflow-hidden">
    <!-- Collapsed Header -->
    <div
      class="p-2 bg-muted/30 cursor-pointer flex items-center justify-between hover:bg-muted/50 transition-colors"
      @click="toggle"
      :aria-expanded="expanded"
      :aria-controls="contentId"
    >
      <div class="flex items-center gap-2 flex-1 min-w-0">
        <slot name="header" />
      </div>
      <slot name="actions" />
    </div>
    <!-- Expanded Content -->
    <div v-if="expanded" :id="contentId" class="p-4 space-y-3 border-t bg-background">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'

const props = defineProps<{ expanded?: boolean }>()
const emit = defineEmits<{ (e: 'update:expanded', value: boolean): void }>()

const expanded = ref(props.expanded ?? false)
const contentId = `collapsible-content-${Math.random().toString(36).slice(2)}`

watch(() => props.expanded, (val) => {
  expanded.value = val ?? false
})

function toggle() {
  expanded.value = !expanded.value
  emit('update:expanded', expanded.value)
}
</script>

<!--
Usage:
<CollapsibleSection :expanded="isExpanded" @update:expanded="setExpanded">
  <template #header> ... </template>
  <template #actions> ... </template>
  ...content...
</CollapsibleSection>
-->
