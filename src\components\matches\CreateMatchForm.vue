<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useUserStore } from '@/stores/user'
import { useFederationsStore, type AgeDivision, type StyleDivision } from '@/stores/federations'
import { type AgendaEntry } from '@/stores/matches'
import { storeToRefs } from 'pinia'
import { api } from '@/api/feathers-client'
import type { MatchData } from '@/api/feathers-client'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

import FormHeader from './create/FormHeader.vue'
import FormProgress from './create/FormProgress.vue'
import FormNavigation from './create/FormNavigation.vue'
import Step1BasicInfo from './create/Step1BasicInfo.vue'
import Step2Location from './create/Step2Location.vue'
import Step3Divisions from './create/Step3Divisions.vue'
import Step4Pricing from './create/Step4Pricing.vue'
import Step5Agenda from './create/Step5Agenda.vue'
import Step6Settings from './create/Step6Settings.vue'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

dayjs.extend(utc)
dayjs.extend(timezone)

// Form data interface
interface CreateMatchFormData {
  name: string
  isActive: boolean
  startDate: string
  endDate?: string
  coverImageUrl?: string
  description?: string
  matchType?: string
  federationId?: number
  address?: string
  city?: string
  country?: string
  postcode?: string
  latitude?: number
  longitude?: number
  phone?: string
  email?: string
  judges: string[]
  agenda: AgendaEntry[]
  licenseRequired?: boolean
  currency?: string
  competitionLevel?: string
  international?: boolean
  withoutLimits?: boolean
  publishAt?: string
  registrationEnds?: string
  maxPlayersAmount?: number
  organizerId?: number
  ageDivisions: AgeDivision[]
  styleDivisions: StyleDivision[]
  payments: Record<string, number>
}

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const federationsStore = useFederationsStore()

const { activeOrganizer } = storeToRefs(userStore)
const { federations } = storeToRefs(federationsStore)

const validationSchema = toTypedSchema(
  z.object({
    name: z.string().min(1, t('validation.required')),
    startDate: z.string().min(1, t('validation.required')),
    endDate: z.string().optional(),
    description: z.string().optional(),
    matchType: z.string().optional(),
    federationId: z.number().optional(),
    coverImageUrl: z.string().url().optional().or(z.literal('')),
    address: z.string().optional(),
    city: z.string().optional(),
    country: z.string().optional(),
    postcode: z.string().optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional().or(z.literal('')),
    currency: z.string().optional(),
    competitionLevel: z.string().optional(),
    maxPlayersAmount: z.number().min(1).optional(),
    publishAt: z.string().optional(),
    registrationEnds: z.string().optional(),
    licenseRequired: z.boolean().optional(),
    international: z.boolean().optional(),
    withoutLimits: z.boolean().optional(),
  })
)

const { validate, setValues, errors } = useForm<CreateMatchFormData>({
  validationSchema,
  initialValues: {
    name: '',
    isActive: true,
    startDate: '',
    endDate: '',
    description: '',
    matchType: '',
    federationId: undefined,
    coverImageUrl: '',
    address: '',
    city: '',
    country: '',
    postcode: '',
    latitude: undefined,
    longitude: undefined,
    phone: '',
    email: '',
    currency: 'PLN',
    competitionLevel: '',
    international: false,
    withoutLimits: false,
    licenseRequired: false,
    publishAt: '',
    registrationEnds: '',
    maxPlayersAmount: undefined,
    judges: [],
    agenda: [],
    payments: {},
    ageDivisions: [],
    styleDivisions: []
  }
})

/* Removed useField. Use errors from useForm instead. */

const formData = ref<CreateMatchFormData>({
  name: '',
  isActive: true,
  startDate: '',
  endDate: '',
  coverImageUrl: '',
  description: '',
  matchType: '',
  federationId: undefined,
  address: '',
  city: '',
  country: '',
  postcode: '',
  latitude: undefined,
  longitude: undefined,
  phone: '',
  email: '',
  judges: [],
  agenda: [],
  licenseRequired: false,
  currency: 'PLN',
  competitionLevel: '',
  international: false,
  withoutLimits: false,
  publishAt: '',
  registrationEnds: '',
  maxPlayersAmount: undefined,
  organizerId: undefined,
  ageDivisions: [],
  styleDivisions: [],
  payments: {}
})

const currentStep = ref(1)
const totalSteps = 6
const isSubmitting = ref(false)
const error = ref<string | null>(null)

const isFederationBased = computed(() => !!formData.value.federationId)

/* Removed duplicate refs: ageDivisions, styleDivisions, payments, judges, agendaItems. Use formData.value properties instead. */

const isDraftSaved = ref(false)
const lastSavedAt = ref<Date | null>(null)
const autoSaveEnabled = ref(true)
const autoSaveInterval = ref<NodeJS.Timeout | null>(null)
const DRAFT_KEY = 'create-match-draft'

const stepErrors = computed(() => {
  const errorCounts = [0, 0, 0, 0, 0, 0]
  if (!formData.value.name) errorCounts[0]++
  if (!formData.value.startDate) errorCounts[0]++
  if (formData.value.coverImageUrl && !/^https?:\/\/.+/.test(formData.value.coverImageUrl)) errorCounts[0]++
  if (formData.value.email && !/.+@.+\..+/.test(formData.value.email)) errorCounts[0]++
  if (formData.value.maxPlayersAmount !== undefined && formData.value.maxPlayersAmount < 1) errorCounts[5]++
  return errorCounts
})

function saveDraft() {
  const draftData = {
    formData: formData.value,
    currentStep: currentStep.value,
    savedAt: new Date().toISOString()
  }
  localStorage.setItem(DRAFT_KEY, JSON.stringify(draftData))
  isDraftSaved.value = true
  lastSavedAt.value = new Date()
}

function loadDraft() {
  const draftJson = localStorage.getItem(DRAFT_KEY)
  if (!draftJson) return false

  try {
    const draftData = JSON.parse(draftJson)
    console.log('[CreateMatchForm] Loading draft data:', draftData.formData)
    console.log('[CreateMatchForm] Draft coordinates:', {
      latitude: draftData.formData.latitude,
      longitude: draftData.formData.longitude,
      types: {
        lat: typeof draftData.formData.latitude,
        lng: typeof draftData.formData.longitude
      }
    })

    // Ensure coordinates are proper numbers or null/undefined
    if (draftData.formData.latitude !== null && draftData.formData.latitude !== undefined) {
      draftData.formData.latitude = Number(draftData.formData.latitude)
      if (!isFinite(draftData.formData.latitude)) {
        draftData.formData.latitude = undefined
      }
    }

    if (draftData.formData.longitude !== null && draftData.formData.longitude !== undefined) {
      draftData.formData.longitude = Number(draftData.formData.longitude)
      if (!isFinite(draftData.formData.longitude)) {
        draftData.formData.longitude = undefined
      }
    }

    console.log('[CreateMatchForm] Normalized coordinates:', {
      latitude: draftData.formData.latitude,
      longitude: draftData.formData.longitude,
      types: {
        lat: typeof draftData.formData.latitude,
        lng: typeof draftData.formData.longitude
      }
    })

    Object.assign(formData.value, draftData.formData)
    currentStep.value = draftData.currentStep || 1
    setValues(draftData.formData)

    console.log('[CreateMatchForm] After assignment - formData coordinates:', {
      latitude: formData.value.latitude,
      longitude: formData.value.longitude,
      types: {
        lat: typeof formData.value.latitude,
        lng: typeof formData.value.longitude
      }
    })

    isDraftSaved.value = true
    lastSavedAt.value = new Date(draftData.savedAt)
    return true
  } catch (err) {
    console.error('Failed to load draft:', err)
    return false
  }
}

function discardDraft() {
  localStorage.removeItem(DRAFT_KEY)
  isDraftSaved.value = false
  lastSavedAt.value = null
  router.go(0) // Reload the page to reset state
}

function toggleAutoSave() {
  autoSaveEnabled.value = !autoSaveEnabled.value
  if (autoSaveEnabled.value) {
    startAutoSave()
  } else {
    stopAutoSave()
  }
}

function startAutoSave() {
  if (autoSaveInterval.value) clearInterval(autoSaveInterval.value)
  autoSaveInterval.value = setInterval(() => {
    if (autoSaveEnabled.value) saveDraft()
  }, 10000)
}

function stopAutoSave() {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value)
    autoSaveInterval.value = null
  }
}

const hasDraft = computed(() => localStorage.getItem(DRAFT_KEY) !== null)

onMounted(() => {
  if (hasDraft.value) {
    loadDraft()
  }
  // Set country based on organizer's country, default to "pl"
  if (activeOrganizer.value && activeOrganizer.value.country) {
    formData.value.country = activeOrganizer.value.country || "pl"
  } else if (!formData.value.country) {
    formData.value.country = "pl"
  }
  // Set publishAt to current time if not set
  if (!formData.value.publishAt) {
    formData.value.publishAt = dayjs().toISOString()
  }
  // Set registrationEnds to one hour before startDate if startDate exists and registrationEnds not set
  if (formData.value.startDate && !formData.value.registrationEnds) {
    formData.value.registrationEnds = dayjs(formData.value.startDate).subtract(1, 'hour').toISOString()
  }
  if (autoSaveEnabled.value) {
    startAutoSave()
  }
  federationsStore.getAllFederations()
})

onUnmounted(() => {
  stopAutoSave()
})

watch(activeOrganizer, (organizer) => {
  if (organizer) {
    formData.value.organizerId = organizer.id
    // Set country based on organizer's country, default to "pl"
    formData.value.country = organizer.country?.toLowerCase() || "pl"
  } else {
    formData.value.country = "pl"
  }
}, { immediate: true })

watch(() => formData.value.country, (newCountry) => {
  if (newCountry && typeof newCountry === 'string') {
    formData.value.country = newCountry.toLowerCase()
  }
})

watch(() => formData.value.startDate, (newStartDate) => {
  if (newStartDate && (!formData.value.endDate || dayjs(newStartDate).isAfter(dayjs(formData.value.endDate)))) {
    formData.value.endDate = newStartDate
  }
  // Set registrationEnds to one hour before startDate if not set or if startDate changes
  if (newStartDate) {
    formData.value.registrationEnds = dayjs(newStartDate).subtract(1, 'hour').toISOString()
  }
})

watch(() => formData.value.endDate, (newEndDate) => {
  if (newEndDate && dayjs(newEndDate).isBefore(dayjs(formData.value.startDate))) {
    formData.value.startDate = newEndDate
  }
})

watch(() => formData.value.federationId, (federationId) => {
  if (federationId) {
    formData.value.ageDivisions = []
    formData.value.styleDivisions = []
  } else {
    formData.value.ageDivisions = []
    formData.value.styleDivisions = []
  }
})

const selectedFederation = computed(() => {
  if (!formData.value.federationId) return null
  return federations.value.find(f => f.id === formData.value.federationId)
})

const availableFederationAgeDivisions = computed(() => selectedFederation.value?.ageDivisions || [])
const availableFederationStyleDivisions = computed(() => selectedFederation.value?.styleDivisions || [])

const agendaDateErrors = computed(() => {
  const errors: Record<number, string> = {}
  if (!formData.value.startDate) return errors
  const start = dayjs(formData.value.startDate)
  const end = formData.value.endDate ? dayjs(formData.value.endDate) : start
  formData.value.agenda?.forEach((item, index) => {
    if (item.date) {
      const agendaDate = dayjs(item.date)
      if (agendaDate.isBefore(start, 'day')) {
        errors[index] = t('validation.agendaDateBeforeStart')
      } else if (agendaDate.isAfter(end, 'day')) {
        errors[index] = t('validation.agendaDateAfterEnd')
      }
    }
  })
  return errors
})

const hasAgendaDateErrors = computed(() => Object.keys(agendaDateErrors.value).length > 0)

const isStepValid = computed(() => {
  switch (currentStep.value) {
    case 1:
      return (
        !!formData.value.name &&
        !!formData.value.startDate &&
        (!formData.value.coverImageUrl || /^https?:\/\/.+/.test(formData.value.coverImageUrl)) &&
        (!formData.value.email || /.+@.+\..+/.test(formData.value.email))
      )
    case 2:
      return true
    case 3:
      return true
    case 4:
      return true
    case 5:
      return !hasAgendaDateErrors.value
    case 6:
      return formData.value.maxPlayersAmount === undefined || formData.value.maxPlayersAmount >= 1
    default:
      return false
  }
})

const canProceed = computed(() => isStepValid.value)
const canGoBack = computed(() => currentStep.value > 1)
const isLastStep = computed(() => currentStep.value === totalSteps)

function nextStep() {
  if (canProceed.value && !isLastStep.value) currentStep.value++
}

function previousStep() {
  if (canGoBack.value) currentStep.value--
}

function goToStep(step: number) {
  if (step >= 1 && step <= totalSteps) currentStep.value = step
}

async function submitForm() {
  if (!activeOrganizer.value) {
    error.value = 'No active organizer found'
    return
  }

  const { valid } = await validate()
  if (!valid) {
    error.value = t('validation.formHasErrors')
    return
  }

  isSubmitting.value = true
  error.value = null

  try {
    const finalData: MatchData = {
      name: formData.value.name ?? '',
      isActive: formData.value.isActive ?? true,
      startDate: formData.value.startDate ?? '',
      endDate: formData.value.endDate ?? '',
      description: formData.value.description ?? '',
      federationId: formData.value.federationId ?? undefined,
      address: formData.value.address ?? '',
      city: formData.value.city ?? '',
      country: formData.value.country ?? '',
      postcode: formData.value.postcode ?? '',
      latitude: formData.value.latitude ?? undefined,
      longitude: formData.value.longitude ?? undefined,
      phone: formData.value.phone ?? '',
      email: formData.value.email ?? '',
      currency: formData.value.currency ?? '',
      competitionLevel: formData.value.competitionLevel ?? '',
      maxPlayersAmount: formData.value.maxPlayersAmount ?? undefined,
      publishAt: formData.value.publishAt ?? '',
      licenseRequired: formData.value.licenseRequired ?? false,
      international: formData.value.international ?? false,
      withoutLimits: formData.value.withoutLimits ?? false,
      organizerId: formData.value.organizerId ?? undefined,
      judges: Array.isArray(formData.value.judges) ? formData.value.judges.filter(j => j && typeof j === 'string' && j.trim()) : [],
      agenda: Array.isArray(formData.value.agenda) ? formData.value.agenda : [],
      ageDivisions: Array.isArray(formData.value.ageDivisions) ? formData.value.ageDivisions : [],
      styleDivisions: Array.isArray(formData.value.styleDivisions) ? formData.value.styleDivisions : [],
      payments: formData.value.payments ? formData.value.payments : {},
    }
    await api.matches.create(finalData)
    discardDraft()
    router.push({ name: 'matches' })
  } catch (err) {
    console.error('Failed to create match:', err)
    error.value = t('matches.matchCreationFailed')
  } finally {
    isSubmitting.value = false
  }
}

const stepTitles = computed(() => [
  t('matches.basicInfo'),
  t('matches.locationInfo'),
  t('matches.divisionsInfo'),
  t('matches.pricingInfo'),
  t('matches.agendaInfo'),
  t('matches.settingsInfo')
])

watch(formData, (newVal) => {
  setValues({ ...newVal })
}, { deep: true })

/* Removed watcher that calls setValues on every formData change to prevent clearing the form during validation. */

</script>

<template>
  <div class=" mx-auto p-6">
    <FormHeader
      :current-step="currentStep"
      :total-steps="totalSteps"
      :step-titles="stepTitles"
      :last-saved-at="lastSavedAt"
      :has-draft="hasDraft"
      :auto-save-enabled="autoSaveEnabled"
      @save-draft="saveDraft"
      @discard-draft="discardDraft"
      @toggle-auto-save="toggleAutoSave"
    />

    <FormProgress
      :current-step="currentStep"
      :total-steps="totalSteps"
      :step-titles="stepTitles"
      :step-errors="stepErrors"
      @go-to-step="goToStep"
    />

    <Alert v-if="error" variant="destructive" class="mb-6">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <Card>
      <CardHeader>
        <CardTitle>{{ stepTitles[currentStep - 1] }}</CardTitle>
        <CardDescription v-if="currentStep === 1">
          {{ t('matches.basicInfo') }} - {{ t('common.required') }}
        </CardDescription>
        <CardDescription v-else>
          {{ stepTitles[currentStep - 1] }} - {{ t('matches.optional') }}
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <Step1BasicInfo
          v-if="currentStep === 1"
          v-model:name="formData.name"
          :name-error="errors.name"
          v-model:match-type="formData.matchType"
          v-model:federation-id="formData.federationId"
          :federations="federations"
          v-model:cover-image-url="formData.coverImageUrl"
          :cover-image-url-error="errors.coverImageUrl"
          v-model:start-date="formData.startDate"
          :start-date-error="errors.startDate"
          v-model:end-date="formData.endDate"
          :end-date-error="errors.endDate"
          v-model:description="formData.description"
          v-model:email="formData.email"
          :email-error="errors.email"
          v-model:phone="formData.phone"
        />
        <Step2Location
          v-if="currentStep === 2"
          v-model:city="formData.city"
          v-model:address="formData.address"
          v-model:country="formData.country"
          v-model:postcode="formData.postcode"
          v-model:latitude="formData.latitude"
          v-model:longitude="formData.longitude"
        />
        <Step3Divisions
          v-if="currentStep === 3"
          v-model:age-divisions="formData.ageDivisions"
          v-model:style-divisions="formData.styleDivisions"
          :is-federation-based="isFederationBased"
          :available-federation-age-divisions="availableFederationAgeDivisions"
          :available-federation-style-divisions="availableFederationStyleDivisions"
          :selected-federation="selectedFederation ? {
            name: selectedFederation.name,
            ageDivisions: selectedFederation.ageDivisions || [],
            styleDivisions: selectedFederation.styleDivisions || []
          } : undefined"
        />
        <Step4Pricing
          v-if="currentStep === 4"
          v-model:payments="formData.payments"
          v-model:currency="formData.currency"
          :age-divisions="formData.ageDivisions"
          :is-federation-based="isFederationBased"
        />
        <Step5Agenda
          v-if="currentStep === 5"
          v-model:agenda-items="formData.agenda"
          v-model:start-date="formData.startDate"
          v-model:end-date="formData.endDate"
        />
        <Step6Settings
          v-if="currentStep === 6"
          v-model:currency="formData.currency"
          v-model:competition-level="formData.competitionLevel"
          v-model:max-players-amount="formData.maxPlayersAmount"
          :max-players-amount-error="errors.maxPlayersAmount"
          v-model:registration-ends="formData.registrationEnds"
          v-model:publish-at="formData.publishAt"
          v-model:license-required="formData.licenseRequired"
          v-model:international="formData.international"
          v-model:without-limits="formData.withoutLimits"
          v-model:judges="formData.judges"
        />
      </CardContent>
    </Card>

    <FormNavigation
      :can-go-back="canGoBack"
      :can-proceed="canProceed"
      :is-last-step="isLastStep"
      :is-submitting="isSubmitting"
      @previous-step="previousStep"
      @next-step="nextStep"
      @submit-form="submitForm"
    />
  </div>
</template>
