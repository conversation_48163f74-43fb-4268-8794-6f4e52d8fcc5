// Display-specific interfaces for presentation logic
// These types are used for UI components and don't represent stored data

export interface DisplayEquipmentCategory {
    id: string | number
    name: string
    short_name: string
    description?: string
    displayOrder?: number
    isActive?: boolean
}

export interface DisplayAgeCategory {
    id: string | number
    name: string
    short_name: string
    min?: number | null
    max?: number | null
}

export interface DisplayStyleCategory {
    id: string | number
    name: string
    short_name: string
}

export interface DisplayDataType {
    id: number
    name: string
    country?: string
    city?: string
    startDate?: string | Date
    endDate?: string | Date
    equipmentCategories: DisplayEquipmentCategory[]
    ageCategories: DisplayAgeCategory[]
    styleCategories: DisplayStyleCategory[]
    federation?: string
    licenseRequired?: boolean
    organizerName: string
    competitionRank: string
    competitionType: string
    distanceKm: number | string
    sunriseTime: string
    sunsetTime: string
    temperatureCelsius: number
    priceAmount: number
    priceCurrency: string
    participantsCount: number
    participantProgress: number
}

// Additional display-specific interfaces can be added here as needed
// for presentation logic that differs from the underlying data models
