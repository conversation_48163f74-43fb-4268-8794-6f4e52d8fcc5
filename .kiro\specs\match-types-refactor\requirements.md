# Requirements Document

## Introduction

This feature involves refactoring the Match-related types in the Archery Points application to consolidate and clean up the multiple local variants and extensions. Currently, the project has several overlapping and inconsistent Match type definitions that create confusion and potential type conflicts. The goal is to establish a clean, consistent type hierarchy based on the feathers client types (<PERSON>, <PERSON><PERSON>ata, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>) and extend them only when necessary for missing nested types or JSON fields.

## Requirements

### Requirement 1

**User Story:** As a developer, I want a clean and consistent Match type hierarchy, so that I can work with Match objects without type conflicts or confusion.

#### Acceptance Criteria

1. WHEN working with Match types THEN the system SHALL use the feathers client types (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>P<PERSON>) as the base types
2. WHEN extending base types THEN the system SHALL only add missing nested types for JSON fields that are not covered by the base types
3. WHEN importing Match types THEN the system SHALL have no duplicate type identifiers or conflicts
4. WHEN using Match types in components THEN the system SHALL provide consistent type definitions across the application

### Requirement 2

**User Story:** As a developer, I want to eliminate duplicate and conflicting type definitions, so that TypeScript compilation is clean and predictable.

#### Acceptance Criteria

1. <PERSON><PERSON><PERSON> compiling the application THEN the system SHALL not produce any "Duplicate identifier" errors for Match types
2. WH<PERSON> extending feathers client types THEN the system SHALL use module augmentation instead of creating separate type definitions
3. WHEN defining optional fields THEN the system SHALL consolidate them into a single, well-organized extension
4. WHEN importing types THEN the system SHALL have a clear import path hierarchy without circular dependencies

### Requirement 3

**User Story:** As a developer, I want properly typed nested objects for JSON fields, so that I can work with complex Match data structures safely.

#### Acceptance Criteria

1. WHEN working with agenda entries THEN the system SHALL provide proper TypeScript types for AgendaEntry objects
2. WHEN working with age divisions THEN the system SHALL provide proper TypeScript types for AgeDivision objects  
3. WHEN working with style divisions THEN the system SHALL provide proper TypeScript types for StyleDivision objects
4. WHEN working with payment entries THEN the system SHALL provide proper TypeScript types for payment objects
5. WHEN working with equipment categories THEN the system SHALL provide proper TypeScript types for equipment category objects

### Requirement 4

**User Story:** As a developer, I want to maintain backward compatibility, so that existing code continues to work after the refactoring.

#### Acceptance Criteria

1. WHEN refactoring types THEN the system SHALL maintain all existing type properties and methods
2. WHEN updating imports THEN the system SHALL ensure all existing usages continue to work
3. WHEN extending types THEN the system SHALL preserve all current functionality
4. WHEN removing duplicate definitions THEN the system SHALL ensure no breaking changes to existing code

### Requirement 5

**User Story:** As a developer, I want to consolidate locally defined Match-related types in components, so that there's a single source of truth for type definitions.

#### Acceptance Criteria

1. WHEN defining TournamentWithMatches interfaces THEN the system SHALL consolidate multiple local definitions into a shared type
2. WHEN defining ExtendedMatchOptionalFields interfaces THEN the system SHALL remove duplicate definitions from components
3. WHEN defining DisplayEquipmentCategory interfaces THEN the system SHALL move local component types to shared type files
4. WHEN using Match types in components THEN the system SHALL import from centralized type definitions instead of defining locally
5. WHEN extending Tournament types THEN the system SHALL provide a consistent TournamentWithMatches type across all components

### Requirement 6

**User Story:** As a developer, I want clear documentation of type extensions, so that I understand what additional fields are available beyond the base feathers types.

#### Acceptance Criteria

1. WHEN extending base types THEN the system SHALL document the purpose of each additional field
2. WHEN creating nested types THEN the system SHALL provide clear interface definitions
3. WHEN organizing type files THEN the system SHALL follow a logical structure that's easy to navigate
4. WHEN adding optional fields THEN the system SHALL clearly indicate which fields are computed vs stored
