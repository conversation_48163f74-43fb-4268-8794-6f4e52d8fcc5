import type { Equipment as ApiEquipment, Federation as ApiFederation } from 'ap-api-feathers'
// src/composables/usePlayerDivisions.ts
import { computed, type Ref } from 'vue'

import type { ExtendedMatch } from '@/types'
import { createSharedComposable } from '@vueuse/core'

interface DivisionDisplay {
  type: 'style' | 'age' | 'sex'
  short_name: string
  name: string
  tooltip?: string
}

function getPlayerAge(birthdate?: string): number | null {
  if (!birthdate) {
    return null
  }
  const today = new Date()
  const birth = new Date(birthdate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

export function usePlayerDivisions(
  activePlayer: Ref<any | null | undefined>,
  match: Ref<Partial<ExtendedMatch> | null | undefined>
) {
  // Style Division
  const styleDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer.value || !activePlayer.value.activeEquipment?.federationStyles || !match.value || !match.value.federation) return null

    console.log('player:', activePlayer.value)

    const equipment = (activePlayer.value as any).activeEquipment
    const styleDivisions = (match.value as any).federation.styleDivisions
    const equipmentStyles = (activePlayer.value as any).activeEquipment.federationStyles
    console.log('equipmentStyles:', equipmentStyles)
    let matchStyles = (match.value as any).styleDivisions

    // only if matchStyles array contains objects, map them to array of strings using short_name property
    matchStyles = matchStyles.map((style: any) => {
      if (typeof style === 'object') {
        return style.short_name
      }
      return style
    })

    console.log('matchStyles:', matchStyles)

    console.log('equipmentStyles:', equipment.federationStyles)
    console.log('styleDivisions:', styleDivisions)

    // get equipment style from equipmentStyles based on federation_name
    const currentEquipmentStyle = equipmentStyles.find((style: any) => style.federation_name === (match.value as any).federation.name)
    console.log('currentEquipmentStyle:', currentEquipmentStyle)


    //filter styleDivisions to only contain items with short_name in matchStyles
    const filteredStyleDivisions = styleDivisions.filter((style: any) => matchStyles.includes(style.short_name))

    console.log('filteredStyleDivisions:', filteredStyleDivisions)

    // finally get style division if matches currentEquipmentStyle short_name
    const matchStyleDivision = filteredStyleDivisions.find((style: any) => style.short_name === currentEquipmentStyle?.category)

    console.log('matchStyleDivision:', matchStyleDivision)


    if (matchStyleDivision) {

      return {
        type: 'style' as const,
        short_name: matchStyleDivision.short_name,
        name: matchStyleDivision.name,
        tooltip: matchStyleDivision.name
      }
    }

    return {
      type: 'style' as const,
      short_name: '?',
      name: 'Unknown',
      tooltip: 'No style for current bow found'
    }

  })

  // Age Division
  const ageDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer.value || !match.value) return null
    const playerAge = getPlayerAge((activePlayer.value as any).birthdate)
    let ageDivObj: any = null

    // match.ageDivisions can be string[] or object[]
    if (Array.isArray((match.value as any).ageDivisions) && playerAge !== null) {
      for (const div of (match.value as any).ageDivisions) {
        if (typeof div === 'string') {
          // Lookup in federation's ageDivisions
          const federationAgeDiv =
            (match.value as any).federation?.ageDivisions?.find(
              (cat: any) => cat.short_name === div
            )
          if (federationAgeDiv) {
            const min = federationAgeDiv.min ?? federationAgeDiv.min_age
            const max = federationAgeDiv.max ?? federationAgeDiv.max_age
            if (
              (min !== undefined && playerAge < min) ||
              (max !== undefined && playerAge > max)
            ) {
              continue
            }
            ageDivObj = federationAgeDiv
            break
          }
        } else if (typeof div === 'object') {
          const min = div.min ?? div.min_age
          const max = div.max ?? div.max_age
          if (
            (min !== undefined && playerAge < min) ||
            (max !== undefined && playerAge > max)
          ) {
            continue
          }
          ageDivObj = div
          break
        }
      }
    }
    // fallback to federation's ageDivisions
    if (!ageDivObj && Array.isArray((match.value as any).federation?.ageDivisions) && playerAge !== null) {
      for (const cat of (match.value as any).federation.ageDivisions) {
        const min = cat.min ?? cat.min_age
        const max = cat.max ?? cat.max_age
        if (
          (min !== undefined && playerAge < min) ||
          (max !== undefined && playerAge > max)
        ) {
          continue
        }
        ageDivObj = cat
        break
      }
    }

    if (ageDivObj) {
      return {
        type: 'age' as const,
        short_name: ageDivObj.short_name || ageDivObj.name || '',
        name: ageDivObj.name || ageDivObj.short_name || '',
        tooltip:
          ageDivObj.name ||
          (ageDivObj.min !== undefined && ageDivObj.max !== undefined
            ? `Age ${ageDivObj.min}-${ageDivObj.max}`
            : '')
      }
    }
    return null
  })

  // Sex Division
  const sexDivision = computed<DivisionDisplay | null>(() => {
    if (!activePlayer.value) return null
    const sex = (activePlayer.value as any).sex
    if (typeof sex === 'string' && sex.trim() !== '') {
      return {
        type: 'sex' as const,
        short_name: sex,
        name: sex === 'M' ? 'Male' : sex === 'F' ? 'Female' : sex,
        tooltip: sex === 'M' ? 'Male' : sex === 'F' ? 'Female' : sex
      }
    }
    return null
  })

  return {
    styleDivision,
    ageDivision,
    sexDivision
  }
}
