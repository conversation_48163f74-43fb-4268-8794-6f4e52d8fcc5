<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useMatchesService } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import SimpleMatchesList from '@/components/matches/SimpleMatchesList.vue'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
} from '@/components/ui/sidebar'
import { Calendar, Users, Trophy, AlertCircle, Plus } from 'lucide-vue-next'
import type { Match } from '@/api/feathers-client'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const matchesService = useMatchesService()
const userStore = useUserStore()
const { activeOrganizer } = storeToRefs(userStore)

const selectedMatch = ref<Match | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)
const organizerMatches = ref<Match[]>([])

// Fetch matches for the active organizer
const fetchOrganizerMatches = async () => {
  if (!activeOrganizer.value?.id) {
    console.warn('No active organizer found')
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const response = await matchesService.findMatches({
      query: {
        organizerId: activeOrganizer.value.id,
        $limit: 100,
        $sort: { startDate: -1 }
      }
    })
    organizerMatches.value = response || []
  } catch (err) {
    console.error('Failed to fetch organizer matches:', err)
    error.value = err instanceof Error ? err : new Error('Failed to fetch matches')
  } finally {
    isLoading.value = false
  }
}

// Group matches by status
const upcomingMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return organizerMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate >= today
  })
})

const pastMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return organizerMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate < today
  })
})

const selectMatch = (match: Match) => {
  selectedMatch.value = match
}

const goToCreateMatch = () => {
  router.push({ name: 'match-create' })
}

// Watch for changes in activeOrganizer and fetch matches when it becomes available
onMounted(async () => {
  if (activeOrganizer.value) {
    await fetchOrganizerMatches()
  }
})
</script>

<template>
  <SidebarProvider>
    <SidebarInset>
      <div class="flex flex-col gap-4 p-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold">{{ t('navigation.organizerMatches') }}</h1>
            <p v-if="activeOrganizer" class="text-muted-foreground">
              {{ activeOrganizer.name }}
            </p>
          </div>
          <Button @click="goToCreateMatch" class="flex items-center gap-2">
            <Plus class="h-4 w-4" />
            {{ t('navigation.createMatch') }}
          </Button>
        </div>

        <div v-if="!activeOrganizer" class="text-center p-8">
          <AlertCircle class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 class="text-lg font-medium mb-2">No Organizer Selected</h3>
          <p class="text-muted-foreground">Please select an organizer profile to view matches.</p>
        </div>

        <div v-else-if="isLoading" class="text-center p-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="mt-4 text-muted-foreground">Loading matches...</p>
        </div>

        <div v-else-if="error" class="text-center p-8">
          <AlertCircle class="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 class="text-lg font-medium mb-2 text-red-600">Error Loading Matches</h3>
          <p class="text-muted-foreground mb-4">{{ error.message }}</p>
          <Button @click="fetchOrganizerMatches" variant="outline">
            Try Again
          </Button>
        </div>

        <div v-else class="grid gap-6">
          <!-- Summary Cards -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Total Matches</CardTitle>
                <Trophy class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ organizerMatches.length }}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Upcoming</CardTitle>
                <Calendar class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ upcomingMatches.length }}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle class="text-sm font-medium">Completed</CardTitle>
                <Users class="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div class="text-2xl font-bold">{{ pastMatches.length }}</div>
              </CardContent>
            </Card>
          </div>

          <!-- Matches Lists -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Upcoming Matches -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Calendar class="h-5 w-5" />
                  Upcoming Matches
                  <Badge variant="secondary">{{ upcomingMatches.length }}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleMatchesList
                  v-if="upcomingMatches.length > 0"
                  :items="upcomingMatches"
                  @select-match="selectMatch"
                />
                <div v-else class="text-center py-8 text-muted-foreground">
                  <Calendar class="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No upcoming matches</p>
                </div>
              </CardContent>
            </Card>

            <!-- Past Matches -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Trophy class="h-5 w-5" />
                  Past Matches
                  <Badge variant="secondary">{{ pastMatches.length }}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleMatchesList
                  v-if="pastMatches.length > 0"
                  :items="pastMatches"
                  @select-match="selectMatch"
                />
                <div v-else class="text-center py-8 text-muted-foreground">
                  <Trophy class="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No past matches</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SidebarInset>

    <!-- Match Details Sidebar -->
    <Sidebar
      v-if="selectedMatch"
      class="sticky top-14 h-[calc(100svh-3.5rem)] border-l"
      collapsible="offcanvas"
    >
      <SidebarContent>
        <MatchDetailsWidget :match="selectedMatch" />
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
