# Design Document

## Overview

This design outlines the refactoring of Match-related types in the Archery Points application to create a clean, consistent type hierarchy. The current system has multiple overlapping type definitions scattered across different files and components, leading to type conflicts and maintenance issues. The solution consolidates all Match-related types around the feathers client base types while providing proper extensions for application-specific needs.

## Architecture

### Current State Analysis

The current type system has several issues:
1. **Duplicate type definitions**: `ExtendedMatchOptionalFields` is defined in both `src/types/match.d.ts` and `src/components/matches/MatchDetailsWidget.vue`
2. **Module augmentation conflicts**: Both `src/types/global.d.ts` and `src/types/extended-match-data.d.ts` augment the same interfaces
3. **Scattered local types**: `TournamentWithMatches` is defined locally in multiple components
4. **Inconsistent naming**: Mix of `ApiMatch`, `ExtendedMatch`, and base `Match` types
5. **Type conflicts**: Duplicate identifier errors for `Match` and `Player` types

### Target Architecture

The new architecture will follow a hierarchical approach:

```
Feathers Client Types (Base)
├── Match, MatchData, MatchQuery, MatchPatch
├── Tournament, TournamentData, etc.
└── Player, PlayerData, etc.

Application Extensions (Augmentation)
├── Match extensions (via module augmentation)
├── Tournament extensions
└── Player extensions

Derived Types (Composition)
├── ExtendedMatch (Match + computed fields)
├── TournamentWithMatches
└── Display-specific interfaces
```

## Components and Interfaces

### 1. Base Type Structure

**File: `src/types/match.ts`** (renamed from `.d.ts`)
- Consolidate all Match-related type extensions
- Remove duplicate definitions
- Provide clear separation between stored and computed fields

### 2. Module Augmentation Strategy

**File: `src/types/feathers-augmentation.ts`** (new)
- Single source for all feathers client augmentations
- Consolidate extensions from `global.d.ts` and `extended-match-data.d.ts`
- Resolve duplicate identifier conflicts

### 3. Shared Component Types

**File: `src/types/tournament.ts`** (new)
- Define `TournamentWithMatches` interface
- Consolidate tournament-related extensions
- Remove local component definitions

### 4. Display-Specific Types

**File: `src/types/display.ts`** (new)
- Move display-specific interfaces like `DisplayEquipmentCategory`
- Separate presentation logic from data types
- Provide computed field interfaces

## Data Models

### Core Type Hierarchy

```typescript
// Base types from feathers client
import type { 
  Match, MatchData, MatchQuery, MatchPatch,
  Tournament, TournamentData,
  Player, PlayerData 
} from '@/api/feathers-client'

// Extended types with computed fields
interface ExtendedMatch extends Match {
  // Computed fields only
  distanceKm?: number
  sunriseTime?: string
  sunsetTime?: string
  temperatureCelsius?: number
  participantProgress?: number
  participantsCount?: number
}

// Tournament with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: ExtendedMatch[]
}

// Display-specific interfaces
interface DisplayEquipmentCategory {
  id: string | number
  name: string
  description?: string
}
```

### JSON Field Type Definitions

For complex JSON fields stored in the database, we'll define proper TypeScript interfaces:

```typescript
// Age category structure
interface AgeCategory {
  id: string | number
  name?: string
  short_name?: string
  abbreviation?: string
  min_age?: number
  min?: number
  max_age?: number
  max?: number
}

// Payment entry structure
interface PaymentEntry {
  amount: number
  currency: string
  category?: string
  description?: string
}

// Equipment category structure
interface EquipmentCategory {
  id: string | number
  name: string
  description?: string
  requirements?: string[]
}
```

### Module Augmentation Structure

```typescript
// Augment feathers client types with application-specific fields
declare module '@/api/feathers-client' {
  interface Match {
    // Application-specific stored fields
    isActive?: boolean
    international?: boolean
    withoutLimits?: boolean
    licenseRequired?: boolean
    judges?: string[]
    agenda?: AgendaEntry[]
    payments?: Record<string, number>
    ageDivisions?: AgeDivision[] | string[]
    styleDivisions?: StyleDivision[] | string[]
  }

  interface MatchData {
    // Same extensions for creation data
    isActive?: boolean
    international?: boolean
    // ... other fields
  }
}
```

## Error Handling

### Type Conflict Resolution

1. **Duplicate Identifier Errors**: Resolve by consolidating augmentations into a single file
2. **Import Conflicts**: Use consistent import aliases and avoid circular dependencies
3. **Type Assertion Issues**: Provide proper type guards for runtime type checking

### Migration Strategy

1. **Phase 1**: Create new consolidated type files
2. **Phase 2**: Update imports in components to use new types
3. **Phase 3**: Remove old type definitions
4. **Phase 4**: Verify no breaking changes

## Testing Strategy

### Type Testing

1. **Compilation Tests**: Ensure TypeScript compiles without errors
2. **Type Compatibility Tests**: Verify existing code works with new types
3. **Import Resolution Tests**: Check all imports resolve correctly

### Component Testing

1. **Component Type Tests**: Verify components accept new type definitions
2. **Props Validation**: Ensure component props work with refactored types
3. **Store Integration**: Test that stores work with new type hierarchy

### Integration Testing

1. **API Integration**: Verify feathers client types work correctly
2. **Data Flow**: Test data flows through components with new types
3. **Runtime Validation**: Ensure runtime behavior matches type definitions

## Implementation Plan

### File Structure Changes

```
src/types/
├── feathers-augmentation.ts    # Single source for module augmentation
├── match.ts                    # Match-related types and extensions
├── tournament.ts               # Tournament-related types
├── display.ts                  # Display-specific interfaces
└── index.ts                    # Re-export all types

# Remove these files:
├── global.d.ts                 # Consolidated into feathers-augmentation.ts
├── extended-match-data.d.ts    # Consolidated into feathers-augmentation.ts
└── match.d.ts                  # Renamed to match.ts
```

### Import Strategy

Components will import types using consistent patterns:

```typescript
// For base feathers types
import type { Match, MatchData } from '@/api/feathers-client'

// For extended types
import type { ExtendedMatch, TournamentWithMatches } from '@/types'

// For display types
import type { DisplayEquipmentCategory } from '@/types/display'
```

### Backward Compatibility

- All existing type properties will be preserved
- Component interfaces will remain unchanged
- Store methods will continue to work with existing signatures
- Migration will be transparent to end users

## Benefits

1. **Type Safety**: Eliminates duplicate identifier errors and type conflicts
2. **Maintainability**: Single source of truth for type definitions
3. **Developer Experience**: Clear, consistent type imports and usage
4. **Scalability**: Easy to extend types for new features
5. **Documentation**: Self-documenting type hierarchy with clear separation of concerns