import { createRouter, createWebHistory } from 'vue-router'

import { useAuthStore } from '@/stores/auth' // Added

import AccountView from '../views/AccountView.vue' // Added
import LoginView from '../views/LoginView.vue' // Added
import MatchesView from '../views/MatchesView.vue'
import NotificationsView from '../views/NotificationsView.vue' // Added
import SettingsView from '../views/SettingsView.vue' // Added

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login', // Added login route
      name: 'login',
      component: LoginView,
      meta: {
        title: 'Login',
        guestOnly: true, // Prevent authenticated users from accessing login page
      },
    },
    {
      path: '/',
      name: 'home',
      component: MatchesView,
      meta: {
        title: 'Home'
      }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
      meta: {
        title: 'About'
      }
    },
    // tournaments route
    {
      path: '/tournaments',
      name: 'tournaments',
      component: () => import('../views/TournamentsView.vue'),
      meta: {
        title: 'Tournaments'
      }
    },
    // matches/Matches related routes
    {
      path: '/matches',
      name: 'matches',
      redirect: { name: 'matches-search' },
      meta: {
        title: 'matches'
      }
    },
    {
      path: '/matches/search',
      name: 'matches-search',
      component: () => import('../views/MatchesView.vue'),
      meta: {
        title: 'Competition Search'
      }
    },
    {
      path: '/matches/signups',
      name: 'matches-signups',
      component: () => import('../views/MyMatchesView.vue'),
      meta: {
        title: 'My Signups',
        requiresAuth: true
      }
    },
    {
      path: '/matches/watched',
      name: 'matches-watched',
      component: () => import('../views/MatchesView.vue'), // Replace with actual view when created
      meta: {
        title: 'Watched matches',
        requiresAuth: true
      }
    },
    {
      path: '/matches/results',
      name: 'matches-results',
      component: () => import('../views/MatchesView.vue'), // Replace with actual view when created
      meta: {
        title: 'Competition Results'
      }
    },
    {
      path: '/matches/archive',
      name: 'matches-archive',
      component: () => import('../views/MatchesView.vue'), // Replace with actual view when created
      meta: {
        title: 'Competition Archive'
      }
    },
    {
      path: '/matches/create',
      name: 'match-create',
      component: () => import('../views/CreateMatchView.vue'),
      meta: {
        title: 'Create Match',
        requiresAuth: true
      }
    },
    // Organizer routes
    {
      path: '/organizer/matches',
      name: 'organizer-matches',
      component: () => import('../views/OrganizerMatchesView.vue'),
      meta: {
        title: 'My Matches',
        requiresAuth: true
      }
    },
    {
      path: '/organizer/tournaments',
      name: 'organizer-tournaments',
      component: () => import('../views/OrganizerTournamentsView.vue'),
      meta: {
        title: 'My Tournaments',
        requiresAuth: true
      }
    },
    {
      path: '/matches/:id',
      name: 'match-details',
      component: () => import('../views/MatchDetailsView.vue'),
      meta: {
        title: 'Match Details'
      }
    },
    {
      path: '/organizers/:id',
      name: 'organizer-details',
      component: () => import('../views/OrganizerDetailsView.vue'),
      meta: {
        title: 'Organizer Details'
      }
    },
    {
      path: '/tournaments/:id',
      name: 'tournament-details',
      component: () => import('../views/TournamentDetailsView.vue'),
      meta: {
        title: 'Tournament Details'
      }
    },
    // Other feature routes
    {
      path: '/statistics',
      name: 'statistics',
      component: () => import('../views/AboutView.vue'), // Replace with actual view when created
      meta: {
        title: 'Statistics'
      }
    },
    {
      path: '/favourites',
      name: 'favourites',
      component: () => import('../views/AboutView.vue'), // Replace with actual view when created
      meta: {
        title: 'Favourites',
        requiresAuth: true
      }
    },
    {
      path: '/messages',
      name: 'messages',
      component: () => import('../views/AboutView.vue'), // Replace with actual view when created
      meta: {
        title: 'Messages',
        requiresAuth: true
      }
    },
    {
      path: '/account',
      name: 'account',
      component: AccountView,
      meta: {
        title: 'My Account',
        requiresAuth: true,
      },
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: {
        title: 'Settings',
        requiresAuth: true,
      },
    },
    {
      path: '/notifications',
      name: 'notifications',
      component: NotificationsView,
      meta: {
        title: 'Notifications',
        requiresAuth: true,
      },
    },
  ],
})

// Navigation Guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Attempt to re-authenticate if user info is not available but a token might be
  // This is important for page reloads
  if (!authStore.user && authStore.feathersClient.authentication.storage.getItem('feathers-jwt')) {
    await authStore.reAuthenticate()
  }

  const isAuthenticated = authStore.isAuthenticated;
  const isGuestRoute = to.matched.some(record => record.meta.guestOnly); // Routes like login, register

  if (!isAuthenticated) {
    // User is NOT authenticated
    if (isGuestRoute) {
      // If trying to access a guest route (e.g., login, register), allow
      next();
    } else {
      // For any other route, redirect to login
      next({ name: 'login', query: { redirect: to.fullPath } });
    }
  } else {
    // User IS authenticated
    if (isGuestRoute) {
      // If an authenticated user tries to access a guest route (e.g., login page),
      // redirect them to the home page.
      next({ name: 'home' });
    } else {
      // Allow access to non-guest routes
      next();
    }
  }

  // Update page title
  if (to.meta.title) {
    document.title = `${to.meta.title} - Archery Points`
  } else {
    document.title = 'Archery Points'
  }
})

export default router
