<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Save, Trash2 } from 'lucide-vue-next'

const { t } = useI18n()

defineProps<{
  currentStep: number
  totalSteps: number
  stepTitles: string[]
  lastSavedAt: Date | null
  hasDraft: boolean
  autoSaveEnabled: boolean
}>()

const emit = defineEmits<{
  (e: 'save-draft'): void
  (e: 'discard-draft'): void
  (e: 'toggle-auto-save'): void
}>()
</script>

<template>
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">{{ t('matches.createMatch') }}</h1>
        <p class="text-muted-foreground mt-2">
          {{ t('matches.step') }} {{ currentStep }} {{ t('matches.of') }} {{ totalSteps }}:
          {{ stepTitles[currentStep - 1] }}
        </p>
      </div>

      <!-- Draft Controls -->
      <div class="flex items-center gap-2">
        <div v-if="lastSavedAt" class="text-sm text-muted-foreground">
          {{ t('matches.lastSaved') }}: {{ lastSavedAt.toLocaleTimeString() }}
        </div>

        <Button @click="emit('save-draft')" variant="outline" size="sm">
          <Save class="w-4 h-4 mr-2" />
          {{ t('matches.saveDraft') }}
        </Button>

        <Button
          v-if="hasDraft"
          @click="emit('discard-draft')"
          variant="outline"
          size="sm"
          class="text-destructive hover:text-destructive"
        >
          <Trash2 class="w-4 h-4 mr-2" />
          {{ t('matches.discardDraft') }}
        </Button>

        <div class="flex items-center gap-2">
          <Switch :checked="autoSaveEnabled" @update:checked="emit('toggle-auto-save')" />
          <Label class="text-sm">{{ t('matches.autoSave') }}</Label>
        </div>
      </div>
    </div>
  </div>
</template>
