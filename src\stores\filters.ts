import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * Global filters store for managing search and filter state across the application.
 * This store manages federation filters and other global filter states that need to be
 * shared between the sidebar and content components.
 */
export const useFiltersStore = defineStore('filters', () => {
  // Federation filter state
  const selectedFederations = ref<string[]>(['ALL'])
  
  // Computed getters
  const isAllFederationsSelected = computed(() => 
    selectedFederations.value.includes('ALL')
  )
  
  const hasSpecificFederationsSelected = computed(() => 
    selectedFederations.value.length > 0 && !isAllFederationsSelected.value
  )
  
  // Actions
  function setFederationFilters(federations: string[]) {
    selectedFederations.value = federations
  }
  
  function clearFederationFilters() {
    selectedFederations.value = ['ALL']
  }
  
  function addFederationFilter(federation: string) {
    if (federation === 'ALL') {
      selectedFederations.value = ['ALL']
    } else {
      // Remove 'ALL' if selecting specific federation
      if (selectedFederations.value.includes('ALL')) {
        selectedFederations.value = [federation]
      } else {
        // Add federation if not already selected
        if (!selectedFederations.value.includes(federation)) {
          selectedFederations.value.push(federation)
        }
      }
    }
  }
  
  function removeFederationFilter(federation: string) {
    if (federation === 'ALL') {
      return // Cannot remove ALL filter
    }
    
    const index = selectedFederations.value.indexOf(federation)
    if (index > -1) {
      selectedFederations.value.splice(index, 1)
      // If no federations selected, default to ALL
      if (selectedFederations.value.length === 0) {
        selectedFederations.value = ['ALL']
      }
    }
  }
  
  function toggleFederationFilter(federation: string) {
    if (federation === 'ALL') {
      selectedFederations.value = ['ALL']
    } else {
      // Remove 'ALL' if selecting specific federation
      if (selectedFederations.value.includes('ALL')) {
        selectedFederations.value = [federation]
      } else {
        // Toggle specific federation
        const index = selectedFederations.value.indexOf(federation)
        if (index > -1) {
          selectedFederations.value.splice(index, 1)
          // If no federations selected, default to ALL
          if (selectedFederations.value.length === 0) {
            selectedFederations.value = ['ALL']
          }
        } else {
          selectedFederations.value.push(federation)
        }
      }
    }
  }
  
  return {
    // State
    selectedFederations,
    
    // Computed
    isAllFederationsSelected,
    hasSpecificFederationsSelected,
    
    // Actions
    setFederationFilters,
    clearFederationFilters,
    addFederationFilter,
    removeFederationFilter,
    toggleFederationFilter,
  }
})
