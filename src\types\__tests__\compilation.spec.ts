import { describe, it, expect } from 'vitest'
import type {
    Match, MatchData, MatchQuery, MatchPatch,
    Tournament, TournamentData, TournamentQuery, TournamentPatch,
    Player, PlayerData, PlayerQuery, PlayerPatch
} from '@/api/feathers-client'
import type {
    ExtendedMatch,
    ExtendedMatchOptionalFields,
    AgeCategory,
    PaymentEntry,
    EquipmentCategory
} from '@/types/match'
import type { TournamentWithMatches } from '@/types/tournament'
import type {
    DisplayEquipmentCategory,
    DisplayAgeCategory,
    DisplayStyleCategory,
    DisplayDataType
} from '@/types/display'

// Test imports to verify type compilation and module augmentation
describe('Type Compilation Tests', () => {
    it('should import base feathers client types without errors', () => {
        // This test verifies that all base types can be imported successfully
        expect(() => {
            // Test that types are available at compile time
            const matchType: Match = {} as Match
            const matchDataType: MatchData = {} as MatchData
            const tournamentType: Tournament = {} as Tournament
            const playerType: Player = {} as Player

            return { matchType, matchDataType, tournamentType, playerType }
        }).not.toThrow()
    })

    it('should import extended match types without errors', () => {
        // This test verifies that extended match types compile correctly
        expect(() => {
            const extendedMatchType: ExtendedMatch = {} as ExtendedMatch
            const optionalFieldsType: ExtendedMatchOptionalFields = {} as ExtendedMatchOptionalFields
            const ageCategoryType: AgeCategory = {} as AgeCategory
            const paymentEntryType: PaymentEntry = {} as PaymentEntry
            const equipmentCategoryType: EquipmentCategory = {} as EquipmentCategory

            return {
                extendedMatchType,
                optionalFieldsType,
                ageCategoryType,
                paymentEntryType,
                equipmentCategoryType
            }
        }).not.toThrow()
    })

    it('should import tournament types without errors', () => {
        // This test verifies that tournament types compile correctly
        expect(() => {
            const tournamentWithMatchesType: TournamentWithMatches = {} as TournamentWithMatches
            return { tournamentWithMatchesType }
        }).not.toThrow()
    })

    it('should import display types without errors', () => {
        // This test verifies that display types compile correctly
        expect(() => {
            const displayEquipmentType: DisplayEquipmentCategory = {} as DisplayEquipmentCategory
            const displayAgeType: DisplayAgeCategory = {} as DisplayAgeCategory
            const displayStyleType: DisplayStyleCategory = {} as DisplayStyleCategory
            const displayDataType: DisplayDataType = {} as DisplayDataType

            return {
                displayEquipmentType,
                displayAgeType,
                displayStyleType,
                displayDataType
            }
        }).not.toThrow()
    })

    it('should import all types from centralized index without errors', () => {
        // This test verifies that the centralized type index works correctly
        expect(() => {
            // Import from centralized index
            const importTest = async () => {
                const types = await import('@/types')
                return types
            }

            return importTest()
        }).not.toThrow()
    })

    it('should load feathers augmentation file without errors', () => {
        // This test verifies that the augmentation file loads correctly
        expect(() => {
            const importTest = async () => {
                // Import the augmentation file to ensure it loads
                await import('@/types/feathers-augmentation')
                return true
            }

            return importTest()
        }).not.toThrow()
    })
})

// Type compatibility and inference tests
describe('Type Compatibility and Inference Tests', () => {
    it('should properly extend Match type with ExtendedMatch', () => {
        // Create a mock Match object
        const baseMatch: Match = {
            id: 1,
            name: 'Test Match',
            organizerId: 1,
            isActive: true,
            judges: undefined // Add judges property as unknown/undefined
        }

        // ExtendedMatch should be compatible with Match
        const extendedMatch: ExtendedMatch = {
            ...baseMatch,
            distanceKm: 10,
            sunriseTime: '06:00',
            sunsetTime: '18:00',
            temperatureCelsius: 20,
            participantProgress: 75,
            participantsCount: 50
        }

        // Verify that ExtendedMatch has all base Match properties
        expect(extendedMatch.id).toBe(1)
        expect(extendedMatch.name).toBe('Test Match')
        expect(extendedMatch.organizerId).toBe(1)

        // Verify that ExtendedMatch has extended properties
        expect(extendedMatch.distanceKm).toBe(10)
        expect(extendedMatch.sunriseTime).toBe('06:00')
        expect(extendedMatch.sunsetTime).toBe('18:00')
        expect(extendedMatch.temperatureCelsius).toBe(20)
        expect(extendedMatch.participantProgress).toBe(75)
        expect(extendedMatch.participantsCount).toBe(50)
    })

    it('should properly extend Tournament type with TournamentWithMatches', () => {
        // Create a mock Tournament object
        const baseTournament: Tournament = {
            id: 1,
            name: 'Test Tournament',
            organizerId: 1
        }

        // Create mock matches
        const matches: ExtendedMatch[] = [
            {
                id: 1,
                name: 'Match 1',
                organizerId: 1,
                isActive: true,
                distanceKm: 5
            },
            {
                id: 2,
                name: 'Match 2',
                organizerId: 1,
                isActive: true,
                distanceKm: 10
            }
        ]

        // TournamentWithMatches should be compatible with Tournament
        const tournamentWithMatches: TournamentWithMatches = {
            ...baseTournament,
            matches
        }

        // Verify that TournamentWithMatches has all base Tournament properties
        expect(tournamentWithMatches.id).toBe(1)
        expect(tournamentWithMatches.name).toBe('Test Tournament')
        expect(tournamentWithMatches.organizerId).toBe(1)

        // Verify that TournamentWithMatches has matches property
        expect(tournamentWithMatches.matches).toHaveLength(2)
        expect(tournamentWithMatches.matches?.[0].name).toBe('Match 1')
        expect(tournamentWithMatches.matches?.[1].name).toBe('Match 2')
    })

    it('should properly type JSON field interfaces', () => {
        // Test AgeCategory interface
        const ageCategory: AgeCategory = {
            id: 1,
            name: 'Senior',
            short_name: 'SR',
            abbreviation: 'S',
            min_age: 18,
            min: 18,
            max_age: 65,
            max: 65
        }

        expect(ageCategory.id).toBe(1)
        expect(ageCategory.name).toBe('Senior')
        expect(ageCategory.min_age).toBe(18)
        expect(ageCategory.max_age).toBe(65)

        // Test PaymentEntry interface
        const paymentEntry: PaymentEntry = {
            amount: 50.00,
            currency: 'EUR',
            category: 'registration',
            description: 'Match registration fee'
        }

        expect(paymentEntry.amount).toBe(50.00)
        expect(paymentEntry.currency).toBe('EUR')
        expect(paymentEntry.category).toBe('registration')

        // Test EquipmentCategory interface
        const equipmentCategory: EquipmentCategory = {
            id: 1,
            name: 'Recurve Bow',
            description: 'Traditional recurve bow',
            requirements: ['bow', 'arrows', 'string']
        }

        expect(equipmentCategory.id).toBe(1)
        expect(equipmentCategory.name).toBe('Recurve Bow')
        expect(equipmentCategory.requirements).toContain('bow')
    })

    it('should properly type display interfaces', () => {
        // Test DisplayEquipmentCategory interface
        const displayEquipment: DisplayEquipmentCategory = {
            id: 1,
            name: 'Recurve Bow',
            short_name: 'RB',
            description: 'Traditional recurve bow',
            displayOrder: 1,
            isActive: true
        }

        expect(displayEquipment.id).toBe(1)
        expect(displayEquipment.name).toBe('Recurve Bow')
        expect(displayEquipment.short_name).toBe('RB')
        expect(displayEquipment.isActive).toBe(true)

        // Test DisplayAgeCategory interface
        const displayAge: DisplayAgeCategory = {
            id: 1,
            name: 'Senior',
            short_name: 'SR',
            min: 18,
            max: 65
        }

        expect(displayAge.id).toBe(1)
        expect(displayAge.name).toBe('Senior')
        expect(displayAge.min).toBe(18)
        expect(displayAge.max).toBe(65)
    })

    it('should handle extended match properties correctly', () => {
        // Create a Match object with extended properties
        const matchWithExtensions: ExtendedMatch = {
            id: 1,
            name: 'Test Match',
            organizerId: 1,
            isActive: true,
            // Extended properties
            international: false,
            withoutLimits: true,
            licenseRequired: false,
            judges: ['Judge 1', 'Judge 2'],
            payments: { registration: 50, equipment: 25 },
            ageDivisions: ['senior', 'junior'],
            styleDivisions: ['recurve', 'compound']
        } as ExtendedMatch

        // Verify base properties
        expect(matchWithExtensions.id).toBe(1)
        expect(matchWithExtensions.name).toBe('Test Match')

        // Verify extended properties
        expect(matchWithExtensions.isActive).toBe(true)
        expect(matchWithExtensions.international).toBe(false)
        expect(matchWithExtensions.judges).toContain('Judge 1')
        expect(matchWithExtensions.payments).toEqual({ registration: 50, equipment: 25 })
        expect(matchWithExtensions.ageDivisions).toContain('senior')
    })
})
