<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface Props {
  latitude?: number | null
  longitude?: number | null
  address?: string
  city?: string
  country?: string
  postcode?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:latitude': [value: number | null]
  'update:longitude': [value: number | null]
  'update:address': [value: string]
  'update:city': [value: string]
  'update:country': [value: string]
  'update:postcode': [value: string]
}>()

const mapContainer = ref<HTMLElement | null>(null)
let map: L.Map | null = null
let marker: <PERSON><PERSON> | null = null

const defaultCenter: L.LatLngTuple = [52.0693, 19.4803] // Poland center
const defaultZoom = 6

// Custom marker icon
const markerIcon = L.divIcon({
  html: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="#ef4444" width="25" height="41">
    <path d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z" stroke="#FFFFFF" stroke-width="10"/>
  </svg>`,
  iconSize: [25, 41],
  iconAnchor: [12.5, 41],
  popupAnchor: [1, -34],
  className: 'leaflet-svg-icon'
})

// Geocoding functions
async function geocodeAddress(address: string, city: string, country: string): Promise<{lat: number, lon: number} | null> {
  try {
    const query = [address, city, country].filter(Boolean).join(', ')
    if (!query.trim()) return null

    const response = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(query)}&format=json&limit=1`)
    const data = await response.json()

    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lon: parseFloat(data[0].lon)
      }
    }
    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

interface NominatimAddress {
  house_number?: string
  road?: string
  city?: string
  town?: string
  village?: string
  country_code?: string
  postcode?: string
}

interface NominatimResponse {
  address?: NominatimAddress
}

async function reverseGeocode(lat: number, lon: number): Promise<NominatimResponse | null> {
  try {
    const response = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=json`)
    const data = await response.json()
    return data
  } catch (error) {
    console.error('Reverse geocoding error:', error)
    return null
  }
}

// Update marker position
function updateMarker(lat: number, lon: number) {
  if (!map) return

  console.log('[LocationPickerMap] updateMarker called with:', { lat, lon, types: { lat: typeof lat, lng: typeof lon } })

  // Validate coordinates to prevent infinite tiles error
  if (
    !isFinite(lat) || !isFinite(lon) ||
    lat < -90 || lat > 90 ||
    lon < -180 || lon > 180
  ) {
    console.warn('[LocationPickerMap] Invalid coordinates provided to updateMarker:', { lat, lon })
    return
  }

  const position: L.LatLngTuple = [lat, lon]

  if (marker) {
    marker.setLatLng(position)
  } else {
    marker = L.marker(position, { icon: markerIcon })
      .addTo(map)
      .bindPopup('Selected location')
  }

  // Ensure zoom level is within valid range - use default if current zoom is invalid
  const currentZoom = map.getZoom()
  let targetZoom: number

  if (isFinite(currentZoom) && currentZoom > 0) {
    targetZoom = Math.max(Math.min(currentZoom, 18), 12)
  } else {
    // Use default zoom if current zoom is invalid (NaN, 0, etc.)
    targetZoom = 13
  }

  console.log('[LocationPickerMap] Setting map view to:', { lat, lon, zoom: targetZoom, currentZoom })
  map.setView(position, targetZoom)
}

// Handle map click
function onMapClick(e: L.LeafletMouseEvent) {
  const { lat, lng } = e.latlng

  // Validate coordinates before processing
  if (!isFinite(lat) || !isFinite(lng) ||
      lat < -90 || lat > 90 ||
      lng < -180 || lng > 180) {
    console.warn('Invalid coordinates from map click:', { lat, lng })
    return
  }

  // Set flag to prevent address-to-coordinate updates
  isUpdatingFromMap.value = true

  emit('update:latitude', lat)
  emit('update:longitude', lng)

  updateMarker(lat, lng)

  // Reverse geocode to get address
  reverseGeocode(lat, lng).then(data => {
    if (data && data.address) {
      const addr = data.address
      emit('update:address', [addr.house_number, addr.road].filter(Boolean).join(' ') || '')
      emit('update:city', addr.city || addr.town || addr.village || '')
      emit('update:country', addr.country_code?.toUpperCase() || '')
      emit('update:postcode', addr.postcode || '')
    }

    // Reset flag after a brief delay to allow updates to complete
    setTimeout(() => {
      isUpdatingFromMap.value = false
    }, 100)
  }).catch(() => {
    // Reset flag even if reverse geocoding fails
    setTimeout(() => {
      isUpdatingFromMap.value = false
    }, 100)
  })
}

// Initialize map
function initMap() {
  if (mapContainer.value && !map) {
    console.log('[LocationPickerMap] Initializing map...')

    map = L.map(mapContainer.value, {
      zoomControl: true,
      attributionControl: true,
      minZoom: 1,
      maxZoom: 18
    })

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 18,
      minZoom: 1,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    map.on('click', onMapClick)

    // Set initial view with validation
    console.log('[LocationPickerMap] initMap - props:', {
      latitude: props.latitude,
      longitude: props.longitude,
      types: { lat: typeof props.latitude, lng: typeof props.longitude }
    })

    // Convert to numbers if they're strings (from form restoration)
    const lat = props.latitude != null ? Number(props.latitude) : null
    const lng = props.longitude != null ? Number(props.longitude) : null

    if (lat != null && lng != null &&
        isFinite(lat) && isFinite(lng) &&
        lat >= -90 && lat <= 90 &&
        lng >= -180 && lng <= 180) {
      console.log('[LocationPickerMap] Using provided coordinates for initial view:', { lat, lng })
      updateMarker(lat, lng)
    } else {
      console.log('[LocationPickerMap] Using default center for initial view - invalid coords:', { lat, lng })
      map.setView(defaultCenter, defaultZoom)
    }

    console.log('[LocationPickerMap] Map initialization complete')
  }
}

// Watch for coordinate changes and update marker (only based on latitude/longitude)
watch([() => props.latitude, () => props.longitude], ([newLat, newLng], [oldLat, oldLng]) => {
  console.log('[LocationPickerMap] Coordinates changed:', {
    newLat, newLng, oldLat, oldLng,
    isFinite: {
      lat: newLat != null ? isFinite(newLat) : false,
      lng: newLng != null ? isFinite(newLng) : false
    },
    inBounds: {
      lat: newLat != null ? (newLat >= -90 && newLat <= 90) : false,
      lng: newLng != null ? (newLng >= -180 && newLng <= 180) : false
    }
  })

  // Convert to numbers if they're strings (from form restoration)
  const lat = newLat != null ? Number(newLat) : null
  const lng = newLng != null ? Number(newLng) : null

  if (lat != null && lng != null && map &&
      isFinite(lat) && isFinite(lng) &&
      lat >= -90 && lat <= 90 &&
      lng >= -180 && lng <= 180) {
    console.log('[LocationPickerMap] Updating marker with valid coordinates:', { lat, lng })
    updateMarker(lat, lng)
  } else {
    console.log('[LocationPickerMap] Skipping marker update - invalid coordinates or no map:', {
      lat, lng, hasMap: !!map,
      isValidLat: lat != null && isFinite(lat) && lat >= -90 && lat <= 90,
      isValidLng: lng != null && isFinite(lng) && lng >= -180 && lng <= 180
    })
  }
}, { immediate: true })

// Track when we're programmatically updating coordinates to prevent loops
const isUpdatingFromMap = ref(false)
const isFormRestore = ref(true)

// Watch for address changes and geocode (only when user manually changes address fields)
let geocodeTimeout: NodeJS.Timeout | null = null
watch([() => props.address, () => props.city, () => props.country], async ([newAddress, newCity, newCountry], [oldAddress, oldCity, oldCountry]) => {
  // Skip geocoding on initial form restore to prevent overriding manually set coordinates
  if (isFormRestore.value) {
    isFormRestore.value = false
    return
  }

  // Skip if we're in the middle of updating from map click
  if (isUpdatingFromMap.value) return

  if (geocodeTimeout) {
    clearTimeout(geocodeTimeout)
  }

  // Only geocode if any address field actually changed and has a value
  const hasAddressChange = (
    (newAddress !== oldAddress && newAddress) ||
    (newCity !== oldCity && newCity) ||
    (newCountry !== oldCountry && newCountry)
  )

  if (hasAddressChange) {
    geocodeTimeout = setTimeout(async () => {
      const result = await geocodeAddress(newAddress || '', newCity || '', newCountry || '')
      if (result && map &&
          isFinite(result.lat) && isFinite(result.lon) &&
          result.lat >= -90 && result.lat <= 90 &&
          result.lon >= -180 && result.lon <= 180) {
        emit('update:latitude', result.lat)
        emit('update:longitude', result.lon)
        updateMarker(result.lat, result.lon)
      }
    }, 1000)
  }
})

function cleanup() {
  if (geocodeTimeout) {
    clearTimeout(geocodeTimeout)
    geocodeTimeout = null
  }
  if (map) {
    try {
      // Remove all event listeners before destroying the map
      map.off()
      // Remove marker if it exists
      if (marker) {
        map.removeLayer(marker)
        marker = null
      }
      // Remove the map instance
      map.remove()
    } catch (error) {
      console.warn('Error during map cleanup:', error)
    } finally {
      map = null
    }
  }
}

function handleResize() {
  if (map && map.getContainer()) {
    try {
      map.invalidateSize()
    } catch (error) {
      console.warn('Error during map resize:', error)
    }
  }
}

let intersectionObserver: IntersectionObserver | null = null

onMounted(() => {
  // Don't initialize immediately - wait for the component to be visible
  intersectionObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting && mapContainer.value && !map) {
        console.log('[LocationPickerMap] Component is now visible, initializing map...')
        intersectionObserver?.disconnect() // Stop observing once initialized
        nextTick(() => {
          setTimeout(() => {
            initMap()
            // After initial map setup, allow address updates to trigger geocoding
            setTimeout(() => {
              isFormRestore.value = false
            }, 500)
          }, 100)
        })
      }
    })
  })

  if (mapContainer.value) {
    intersectionObserver.observe(mapContainer.value)
  }

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  cleanup()
  if (intersectionObserver) {
    intersectionObserver.disconnect()
    intersectionObserver = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div class="space-y-4">
    <div class="text-sm text-muted-foreground">
      Click on the map to select a location, or enter address details above to automatically place the marker. Map marker position is based on latitude/longitude coordinates.
    </div>
    <div ref="mapContainer" class="w-full h-64 border rounded-lg"></div>
  </div>
</template>

<style scoped>
:deep(.leaflet-control-container .leaflet-top,
      .leaflet-control-container .leaflet-bottom) {
  z-index: 20;
}

:deep(.leaflet-popup-content) {
  margin: 8px;
  font-size: 0.75rem;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 0.5rem;
}
</style>
